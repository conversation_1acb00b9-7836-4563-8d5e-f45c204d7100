# 加密解密API文档

## 概述

本文档描述了数据提取审计系统中的加密解密API接口。这些API使用AES加密算法，密钥从数据库字典配置中获取。

## 基础信息

- **基础路径**: `/df/api/encryption`
- **内容类型**: `application/json`
- **响应格式**: 统一使用 `Result<T>` 包装

## API接口

### 1. 单个文本加密

**接口地址**: `POST /df/api/encryption/encrypt/single`

**请求参数**:
```json
{
  "plainText": "需要加密的明文"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "encryptedText": "加密后的密文",
    "successCount": 1,
    "failureCount": 0
  }
}
```

### 2. 批量文本加密

**接口地址**: `POST /df/api/encryption/encrypt/batch`

**请求参数**:
```json
{
  "plainTexts": [
    "明文1",
    "明文2",
    "明文3"
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "encryptedTexts": [
      "密文1",
      "密文2",
      "密文3"
    ],
    "encryptionMap": {
      "明文1": "密文1",
      "明文2": "密文2",
      "明文3": "密文3"
    },
    "successCount": 3,
    "failureCount": 0,
    "failures": []
  }
}
```

### 3. 单个文本解密

**接口地址**: `POST /df/api/encryption/decrypt/single`

**请求参数**:
```json
{
  "encryptedText": "需要解密的密文"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "plainText": "解密后的明文",
    "successCount": 1,
    "failureCount": 0
  }
}
```

### 4. 批量文本解密

**接口地址**: `POST /df/api/encryption/decrypt/batch`

**请求参数**:
```json
{
  "encryptedTexts": [
    "密文1",
    "密文2",
    "密文3"
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "plainTexts": [
      "明文1",
      "明文2",
      "明文3"
    ],
    "decryptionMap": {
      "密文1": "明文1",
      "密文2": "明文2",
      "密文3": "明文3"
    },
    "successCount": 3,
    "failureCount": 0,
    "failures": []
  }
}
```

### 5. 检查加密服务状态

**接口地址**: `GET /df/api/encryption/status`

**响应示例**:
```json
{
  "code": 200,
  "message": "加密服务正常",
  "data": true
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `500`: 服务器内部错误

### 错误响应示例

```json
{
  "code": 400,
  "message": "明文不能为空",
  "data": null
}
```

```json
{
  "code": 500,
  "message": "加密失败: 加密密钥为空",
  "data": null
}
```

## 使用说明

### 前置条件

1. 确保数据库中已配置加密密钥
2. 字典表 `dea_dictionary` 中存在编码为 `SYSTEM_CONFIG` 的记录
3. 字典项表 `dea_dictionary_item` 中存在编码为 `DATABASE_PASSWORD_KEY` 的配置项

### 批量操作说明

- 批量操作支持部分成功，即使某些文本加密/解密失败，其他成功的结果仍会返回
- 失败的详细信息会在 `failures` 字段中返回
- `successCount` 和 `failureCount` 字段提供统计信息

### 安全注意事项

1. 加密密钥应定期更换
2. API调用应通过HTTPS进行
3. 敏感数据不应在日志中记录
4. 建议对API访问进行权限控制

## 测试示例

### 使用curl测试

```bash
# 单个文本加密
curl -X POST http://localhost:8080/df/api/encryption/encrypt/single \
  -H "Content-Type: application/json" \
  -d '{"plainText":"test123"}'

# 批量文本加密
curl -X POST http://localhost:8080/df/api/encryption/encrypt/batch \
  -H "Content-Type: application/json" \
  -d '{"plainTexts":["test1","test2","test3"]}'

# 检查服务状态
curl -X GET http://localhost:8080/df/api/encryption/status
```
