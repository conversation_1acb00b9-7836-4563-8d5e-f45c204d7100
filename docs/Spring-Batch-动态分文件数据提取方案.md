# Spring Batch + EasyExcel + 游标 动态分文件大批量数据提取方案

## 方案概述

本方案实现了基于Spring Batch的大批量数据提取功能，支持：
- **动态分文件**：根据配置的记录数阈值自动切换文件
- **游标读取**：使用数据库游标进行内存友好的大批量数据读取
- **并发写入**：支持异步并发写入多个CSV文件
- **内存监控**：实时监控内存使用情况并自适应调整处理策略
- **智能缓冲**：基于内存使用情况的智能缓冲区管理

## 核心组件

### 1. 配置管理
- `BatchExportConfig`: 批量导出配置类
- `application.yml`: 配置文件

### 2. 文件管理
- `FileManager`: 文件管理器接口
- `FileManagerImpl`: 文件管理器实现，负责动态文件切换

### 3. Spring Batch组件
- `CursorItemReader`: 游标数据读取器
- `DataProcessor`: 数据处理器，负责数据清洗和格式化
- `EnhancedDynamicCsvWriter`: 增强版动态分文件写入器

### 4. 高级优化组件
- `MemoryAwareWriter`: 内存感知写入器
- `ConcurrentFileWriter`: 并发文件写入器
- `FileStatisticsListener`: 文件统计监听器

### 5. 服务层
- `EnhancedBatchJobService`: 增强的批处理作业服务
- `TaskExecutionServiceImpl`: 任务执行服务（已集成）

## 配置说明

### application.yml配置
```yaml
# 批量导出配置
batch:
  export:
    max-records-per-file: 50000    # 每个文件5万条记录
    output-base-path: /data/export # 输出路径
    file-prefix: data_export       # 文件名前缀
    chunk-size: 2000              # chunk处理大小
    fetch-size: 1000              # 游标fetch大小
    write-threads: 3              # 并行写入线程数
    memory-threshold: 0.8         # 内存使用率阈值
    enable-async-write: true      # 启用异步写入
    enable-compression: true      # 启用文件压缩
    buffer-flush-threshold: 1000  # 缓冲区刷新阈值
```

## 使用方法

### 1. 基本使用
```java
@Autowired
private EnhancedBatchJobService enhancedBatchJobService;

// 启动数据提取作业
ExtractionTask task = getExtractionTask();
JobExecution jobExecution = enhancedBatchJobService.startExtraction(task);

// 监控作业执行
while (!enhancedBatchJobService.isJobCompleted(jobExecution)) {
    double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);
    log.info("处理进度: {:.2f}%", progress);
    Thread.sleep(2000);
}

// 检查执行结果
if (enhancedBatchJobService.isJobSuccessful(jobExecution)) {
    log.info("数据提取完成");
} else {
    log.error("数据提取失败");
}
```

### 2. 自定义配置使用
```java
// 使用自定义每文件记录数
Integer maxRecordsPerFile = 100000; // 每文件10万条
String outputPath = "/custom/path";
JobExecution jobExecution = enhancedBatchJobService.startExtraction(
    task, maxRecordsPerFile, outputPath);
```

### 3. 简化使用（兼容现有代码）
```java
String sql = "SELECT * FROM large_table";
Long dataSourceId = 1L;
Long taskId = 123L;

JobExecution jobExecution = enhancedBatchJobService.startExtractionSimple(
    sql, dataSourceId, taskId);
```

## 处理流程

### 1. 数据读取阶段
- 使用`CursorItemReader`建立数据库游标连接
- 设置合适的fetch size优化网络传输
- 逐批读取数据，避免内存溢出

### 2. 数据处理阶段
- `DataProcessor`对数据进行清洗和格式化
- 处理时间类型、数值类型、字符串类型的格式化
- 清理特殊字符，确保CSV格式正确

### 3. 动态分文件写入阶段
- `FileManager`监控当前文件记录数
- 达到阈值时自动切换到新文件
- `EnhancedDynamicCsvWriter`管理多文件缓冲区

### 4. 内存优化阶段
- `MemoryAwareWriter`实时监控内存使用率
- 内存紧张时自动调整批次大小
- 触发垃圾回收释放内存

### 5. 并发写入阶段
- `ConcurrentFileWriter`使用线程池异步写入
- 支持多文件并发写入提高性能
- 智能错误处理和重试机制

## 性能特性

### 1. 内存使用优化
- 游标读取避免一次性加载大量数据
- 智能缓冲区管理，根据内存情况调整
- 内存使用率超过80%时自动优化

### 2. 处理性能
- 支持大批量数据处理（千万级别）
- 并发写入提高I/O性能
- 可配置的chunk大小和fetch大小

### 3. 文件管理
- 自动分文件避免单文件过大
- 支持自定义文件命名规则
- 文件大小和记录数可配置

## 监控和统计

### 1. 执行监控
```java
// 获取作业执行摘要
String summary = enhancedBatchJobService.getJobExecutionSummary(jobExecution);

// 获取处理进度
double progress = enhancedBatchJobService.getProgressPercentage(jobExecution);

// 检查作业状态
BatchStatus status = enhancedBatchJobService.getJobStatus(jobExecution);
```

### 2. 统计信息
- 总处理记录数
- 生成文件数量
- 平均每文件记录数
- 处理速度（记录/秒）
- 内存使用情况

### 3. 日志输出
系统会自动输出详细的执行日志，包括：
- 作业开始和结束时间
- 各阶段执行时间
- 内存使用统计
- 文件生成统计
- 性能指标

## 错误处理

### 1. 容错机制
- 支持跳过异常记录（最多1000条）
- 支持重试机制（最多3次）
- 自动处理瞬时数据库连接异常

### 2. 资源清理
- 自动关闭数据库连接和游标
- 清理临时文件和缓冲区
- 线程池优雅关闭

### 3. 异常监控
- 详细的异常日志记录
- 作业失败时的状态保存
- 支持作业重启和恢复

## 扩展性

### 1. 自定义处理器
可以扩展`DataProcessor`实现自定义的数据处理逻辑：
```java
@Component
public class CustomDataProcessor extends DataProcessor {
    @Override
    public CsvOutputModel process(CsvOutputModel item) throws Exception {
        // 自定义处理逻辑
        return super.process(item);
    }
}
```

### 2. 自定义写入器
可以扩展写入器支持其他格式：
```java
@Component
public class CustomFileWriter extends EnhancedDynamicCsvWriter {
    // 自定义写入逻辑
}
```

### 3. 自定义监听器
可以添加自定义的作业监听器：
```java
@Component
public class CustomJobListener implements JobExecutionListener {
    // 自定义监听逻辑
}
```

## 最佳实践

### 1. 配置优化
- 根据服务器内存调整chunk-size和fetch-size
- 根据磁盘I/O能力调整write-threads
- 根据业务需求调整max-records-per-file

### 2. 性能调优
- 大数据量时适当增加fetch-size
- 内存充足时可以增加chunk-size
- SSD存储时可以增加并发写入线程数

### 3. 监控建议
- 定期检查内存使用情况
- 监控文件生成速度
- 关注错误日志和重试情况

## 注意事项

1. **数据库连接**：确保数据库连接池配置足够支持长时间的游标操作
2. **磁盘空间**：确保输出目录有足够的磁盘空间
3. **内存配置**：建议JVM堆内存至少4GB以上
4. **网络稳定性**：长时间运行需要稳定的网络连接
5. **权限设置**：确保应用有输出目录的写入权限

## 版本信息

- **版本**: v1.0
- **创建时间**: 2024年12月
- **Spring Boot版本**: 2.x
- **Spring Batch版本**: 4.x
- **EasyExcel版本**: 3.2.1
