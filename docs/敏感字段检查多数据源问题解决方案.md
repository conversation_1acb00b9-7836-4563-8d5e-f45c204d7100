# 敏感字段检查多数据源问题解决方案

## 问题描述

在多数据源环境下，`submitTask`提交任务系统报错，错误信息显示：

```
Table 'extractaudit.dws_metadata_column_classify' doesn't exist
```

### 根本原因

1. **数据源配置问题**：`SensitiveFieldServiceImpl.checkSensitiveFields()`方法使用`@DS("tianyu")`注解指定使用Oracle数据源
2. **表不存在**：目标表`DWS_METADATA_COLUMN_CLASSIFY`在tianyu数据源中不存在或不可访问
3. **异常处理不当**：当tianyu数据源不可用时，系统没有合适的降级处理机制
4. **多数据源切换失败**：在异常情况下可能回退到默认数据源（extractaudit），导致在错误的数据库中查找表

## 解决方案

### 1. 增强敏感字段服务的容错性

#### 修改文件：`SensitiveFieldServiceImpl.java`

**主要改进：**
- 添加数据源可用性检查
- 实现降级模式（fallback）
- 增强异常处理
- 添加配置化的容错机制

**关键特性：**
```java
@Value("${custom.sensitive-field.enable-fallback:true}")
private boolean enableFallback;

@Value("${custom.sensitive-field.datasource-timeout:5000}")
private int datasourceTimeout;
```

**数据源健康检查：**
```java
private boolean checkTianyuDataSourceAvailability() {
    // 切换到tianyu数据源并测试连接
    // 超时控制和异常处理
}
```

### 2. 任务提交服务的异常处理

#### 修改文件：`TaskCreationServiceImpl.java`

**主要改进：**
- 增加敏感字段检查的异常捕获
- 配置化的失败策略
- 详细的日志记录

**配置属性：**
```java
@Value("${custom.sensitive-field.fail-on-error:false}")
private boolean failOnSensitiveFieldCheckError;
```

### 3. 配置文件更新

#### 修改文件：`application-dev.yml`

**新增配置：**
```yaml
custom:
  sensitive-field:
    # 是否启用降级模式（当tianyu数据源不可用时跳过敏感字段检查）
    enable-fallback: true
    # 是否在敏感字段检查失败时中止任务提交
    fail-on-error: false
    # 数据源连接超时时间（毫秒）
    datasource-timeout: 5000
```

### 4. Mapper接口优化

#### 修改文件：`SensitiveFieldMapper.java`

**改进：**
- 在接口级别添加`@DS("tianyu")`注解，确保数据源切换的一致性

## 配置说明

### 降级模式配置

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `custom.sensitive-field.enable-fallback` | `true` | 启用降级模式，当tianyu数据源不可用时跳过敏感字段检查 |
| `custom.sensitive-field.fail-on-error` | `false` | 敏感字段检查失败时是否中止任务提交 |
| `custom.sensitive-field.datasource-timeout` | `5000` | 数据源连接超时时间（毫秒） |

### 运行模式

#### 1. 生产模式（严格模式）
```yaml
custom:
  sensitive-field:
    enable-fallback: false
    fail-on-error: true
```
- 敏感字段检查失败时中止任务提交
- 确保数据安全合规

#### 2. 开发模式（宽松模式）
```yaml
custom:
  sensitive-field:
    enable-fallback: true
    fail-on-error: false
```
- 允许在数据源不可用时继续任务提交
- 便于开发和测试

## 测试验证

### 单元测试

创建了`SensitiveFieldServiceFallbackTest.java`测试类，覆盖以下场景：

1. **数据源异常 + 降级模式启用**：返回空结果，不抛异常
2. **数据源异常 + 降级模式禁用**：抛出异常
3. **Mapper异常 + 降级模式启用**：跳过异常表，继续处理
4. **Mapper异常 + 降级模式禁用**：抛出异常
5. **正常操作**：正确返回敏感字段检查结果

### 集成测试建议

1. **tianyu数据源不可用场景**
   - 停止Oracle数据库服务
   - 验证任务提交是否能正常完成

2. **表不存在场景**
   - 临时重命名目标表
   - 验证降级机制是否生效

3. **网络超时场景**
   - 模拟网络延迟
   - 验证超时控制是否有效

## 监控和日志

### 关键日志点

1. **数据源健康检查**
   ```
   tianyu数据源连接检查成功/失败
   ```

2. **降级模式触发**
   ```
   tianyu数据源不可用，启用降级模式，跳过敏感字段检查
   ```

3. **异常处理**
   ```
   敏感字段检查失败，但配置允许继续提交任务
   ```

### 监控指标建议

- tianyu数据源可用性
- 敏感字段检查成功率
- 降级模式触发频率
- 任务提交成功率

## 部署注意事项

1. **配置检查**：确认各环境的配置符合预期
2. **数据源验证**：确认tianyu数据源配置正确
3. **表结构确认**：验证`DWS_METADATA_COLUMN_CLASSIFY`表在tianyu数据源中存在
4. **权限检查**：确认应用有权限访问目标表
5. **监控设置**：配置相关监控和告警

## 回滚方案

如果新方案出现问题，可以通过以下配置快速回滚到严格模式：

```yaml
custom:
  sensitive-field:
    enable-fallback: false
    fail-on-error: true
```

这样会恢复到原有的严格检查模式，但仍保留了更好的异常处理和日志记录。
