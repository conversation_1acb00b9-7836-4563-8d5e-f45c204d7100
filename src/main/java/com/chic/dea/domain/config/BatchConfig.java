package com.chic.dea.domain.config;

import com.chic.dea.domain.batch.*;
import com.chic.dea.domain.model.CsvOutputModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.repository.support.JobRepositoryFactoryBean;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.support.DatabaseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Spring Batch配置类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Configuration
@EnableBatchProcessing
public class BatchConfig {

    @Autowired
    private BatchExportConfig config;

    @Autowired
    private CursorItemReader cursorItemReader;

    @Autowired
    private DataProcessor dataProcessor;

    @Autowired
    private EnhancedDynamicCsvWriter enhancedDynamicCsvWriter;

    @Autowired
    private FileStatisticsListener fileStatisticsListener;
    
    /**
     * 动态数据提取Step
     */
    @Bean
    public Step dynamicExportStep(@Qualifier("customJobRepository") JobRepository jobRepository,
                                  PlatformTransactionManager transactionManager) {
        return new StepBuilder("dynamicExportStep")
                .repository(jobRepository)
                .transactionManager(transactionManager)
                .<CsvOutputModel, CsvOutputModel>chunk(config.getChunkSize())
                .reader(cursorItemReader)
                .processor(dataProcessor)
                .writer(enhancedDynamicCsvWriter)
                .faultTolerant()
                .skipLimit(1000)
                .skip(Exception.class)
                .retryLimit(3)
                .retry(TransientDataAccessException.class)
                .listener(fileStatisticsListener)
                .build();
    }
    
    /**
     * 动态数据提取Job
     */
    @Bean
    public Job dynamicDataExtractionJob(@Qualifier("customJobRepository") JobRepository jobRepository,
                                        PlatformTransactionManager transactionManager) {
        return new JobBuilder("dynamicDataExtractionJob")
                .repository(jobRepository)
                .incrementer(new RunIdIncrementer())
                .start(dynamicExportStep(jobRepository, transactionManager))
                .listener(fileStatisticsListener)
                .build();
    }
    
    /**
     * 自定义JobRepository配置
     * 明确使用主数据源(MySQL)，避免在多数据源环境下选择错误的数据源
     * 注意：方法名不能是jobRepository，会与Spring Batch自动配置冲突
     */
    @Bean("customJobRepository")
    public JobRepository customJobRepository(DataSource dataSource,
                                           PlatformTransactionManager transactionManager) throws Exception {
        log.info("配置自定义JobRepository使用主数据源(MySQL)");
        JobRepositoryFactoryBean factory = new JobRepositoryFactoryBean();
        factory.setDataSource(dataSource);
        factory.setTransactionManager(transactionManager);
        factory.setDatabaseType(DatabaseType.MYSQL.getProductName());
        factory.setTablePrefix("BATCH_");
        factory.setIsolationLevelForCreate("ISOLATION_READ_COMMITTED");
        factory.afterPropertiesSet();
        return factory.getObject();
    }
}
