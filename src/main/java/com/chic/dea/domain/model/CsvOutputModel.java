package com.chic.dea.domain.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * CSV输出模型
 * 动态字段支持
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class CsvOutputModel {
    
    /**
     * 动态字段数据
     */
    private Map<String, Object> data = new LinkedHashMap<>();
    
    /**
     * 行号（用于调试和追踪）
     */
    private Long rowNumber;
    
    /**
     * 添加字段数据
     */
    public void addField(String fieldName, Object value) {
        data.put(fieldName, value);
    }
    
    /**
     * 获取字段值
     */
    public Object getFieldValue(String fieldName) {
        return data.get(fieldName);
    }
    
    /**
     * 获取所有字段名
     */
    public String[] getFieldNames() {
        return data.keySet().toArray(new String[0]);
    }
    
    /**
     * 获取所有字段值
     */
    public Object[] getFieldValues() {
        return data.values().toArray();
    }
    
    /**
     * 检查是否包含字段
     */
    public boolean hasField(String fieldName) {
        return data.containsKey(fieldName);
    }
    
    /**
     * 获取字段数量
     */
    public int getFieldCount() {
        return data.size();
    }
    
    /**
     * 清空数据
     */
    public void clear() {
        data.clear();
        rowNumber = null;
    }
    
    /**
     * 从Map构建
     */
    public static CsvOutputModel fromMap(Map<String, Object> sourceMap) {
        CsvOutputModel model = new CsvOutputModel();
        if (sourceMap != null) {
            model.data.putAll(sourceMap);
        }
        return model;
    }
    
    /**
     * 转换为Map
     */
    public Map<String, Object> toMap() {
        return new LinkedHashMap<>(data);
    }
}
