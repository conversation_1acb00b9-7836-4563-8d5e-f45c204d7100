package com.chic.dea.domain.batch;

import com.chic.dea.domain.model.CsvOutputModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.List;

/**
 * 内存感知的写入器
 * 监控内存使用情况并自适应调整处理策略
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class MemoryAwareWriter implements ItemWriter<CsvOutputModel> {
    
    @Autowired
    private DynamicCsvItemWriter delegate;
    
    private final MemoryMXBean memoryBean;
    private final double memoryThreshold = 0.8; // 80%内存使用率阈值
    private final double criticalMemoryThreshold = 0.9; // 90%临界阈值
    
    public MemoryAwareWriter() {
        this.memoryBean = ManagementFactory.getMemoryMXBean();
    }
    
    @Override
    public void write(List<? extends CsvOutputModel> items) throws Exception {
        // 检查内存使用率
        MemoryUsage heapUsage = checkMemoryUsage();
        
        // 根据内存情况动态调整批次大小
        List<? extends CsvOutputModel> adjustedItems = adjustBatchSizeByMemory(items, heapUsage);
        
        // 如果内存使用率过高，分批处理
        if (getMemoryUsageRatio(heapUsage) > memoryThreshold) {
            processBatchesWithMemoryControl(adjustedItems);
        } else {
            delegate.write(adjustedItems);
        }
    }
    
    /**
     * 检查内存使用情况
     */
    private MemoryUsage checkMemoryUsage() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        double usageRatio = getMemoryUsageRatio(heapUsage);
        
        if (usageRatio > criticalMemoryThreshold) {
            log.warn("内存使用率达到临界值: {:.2f}%, 触发垃圾回收", usageRatio * 100);
            System.gc();
            
            // 重新检查内存使用率
            heapUsage = memoryBean.getHeapMemoryUsage();
            usageRatio = getMemoryUsageRatio(heapUsage);
            log.info("垃圾回收后内存使用率: {:.2f}%", usageRatio * 100);
        } else if (usageRatio > memoryThreshold) {
            log.warn("内存使用率过高: {:.2f}%, 建议优化处理策略", usageRatio * 100);
        }
        
        return heapUsage;
    }
    
    /**
     * 根据内存情况调整批次大小
     */
    private List<? extends CsvOutputModel> adjustBatchSizeByMemory(List<? extends CsvOutputModel> items, 
                                                                   MemoryUsage heapUsage) {
        double usageRatio = getMemoryUsageRatio(heapUsage);
        
        // 内存紧张时减少批次大小
        if (usageRatio > memoryThreshold && items.size() > 500) {
            int adjustedSize = Math.max(100, items.size() / 2);
            log.debug("内存使用率{:.2f}%，调整批次大小从{}到{}", usageRatio * 100, items.size(), adjustedSize);
            return items.subList(0, adjustedSize);
        }
        
        return items;
    }
    
    /**
     * 在内存控制下分批处理
     */
    private void processBatchesWithMemoryControl(List<? extends CsvOutputModel> items) throws Exception {
        int batchSize = calculateOptimalBatchSize();
        
        for (int i = 0; i < items.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, items.size());
            List<? extends CsvOutputModel> batch = items.subList(i, endIndex);
            
            // 处理批次前检查内存
            MemoryUsage beforeUsage = memoryBean.getHeapMemoryUsage();
            
            delegate.write(batch);
            
            // 处理批次后检查内存
            MemoryUsage afterUsage = memoryBean.getHeapMemoryUsage();
            double afterRatio = getMemoryUsageRatio(afterUsage);
            
            log.debug("批次处理完成，内存使用率: {:.2f}%", afterRatio * 100);
            
            // 如果内存使用率仍然很高，暂停一下让GC有机会运行
            if (afterRatio > criticalMemoryThreshold) {
                log.warn("内存使用率过高，暂停处理以允许垃圾回收");
                Thread.sleep(100);
                System.gc();
                Thread.sleep(100);
            }
        }
    }
    
    /**
     * 计算最优批次大小
     */
    private int calculateOptimalBatchSize() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        double usageRatio = getMemoryUsageRatio(heapUsage);
        
        if (usageRatio > 0.85) {
            return 50;  // 内存紧张时使用小批次
        } else if (usageRatio > 0.7) {
            return 200; // 中等内存使用时使用中等批次
        } else {
            return 500; // 内存充足时使用大批次
        }
    }
    
    /**
     * 获取内存使用率
     */
    private double getMemoryUsageRatio(MemoryUsage heapUsage) {
        return (double) heapUsage.getUsed() / heapUsage.getMax();
    }
    
    /**
     * 获取内存使用统计信息
     */
    public String getMemoryStatistics() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        double usageRatio = getMemoryUsageRatio(heapUsage);
        
        return String.format("内存统计 - 已用: %.2f MB, 总计: %.2f MB, 使用率: %.2f%%",
                heapUsage.getUsed() / (1024.0 * 1024.0),
                heapUsage.getMax() / (1024.0 * 1024.0),
                usageRatio * 100);
    }
    
    /**
     * 检查是否需要内存优化
     */
    public boolean needsMemoryOptimization() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return getMemoryUsageRatio(heapUsage) > memoryThreshold;
    }
}
