package com.chic.dea.domain.batch;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.database.entity.DataSource;
import com.chic.dea.domain.model.CsvOutputModel;
import com.chic.dea.domain.service.DataSourceService;
import com.chic.dea.domain.service.EncryptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.*;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import static com.chic.dea.infrastructure.general.util.DataSourceConnectionUtil.getConnection;

/**
 * 游标数据读取器
 * 使用数据库游标进行大批量数据读取
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class CursorItemReader implements ItemReader<CsvOutputModel> {
    
    @Autowired
    private BatchExportConfig config;
    
    @Autowired
    private DataSourceService dataSourceService;

    @Autowired
    private EncryptionService encryptionService;
    
    private Connection connection;
    private PreparedStatement statement;
    private ResultSet resultSet;
    private ResultSetMetaData metaData;
    private String[] columnNames;
    private final AtomicLong readCount = new AtomicLong(0);
    
    // 任务相关参数
    private String sql;
    private Long dataSourceId;
    private boolean initialized = false;
    
    /**
     * 初始化读取器
     */
    public void initialize(String sql, Long dataSourceId) {
        this.sql = sql;
        this.dataSourceId = dataSourceId;
        this.initialized = false;
        log.info("初始化游标读取器, dataSourceId: {}", dataSourceId);
    }
    
    @Override
    public CsvOutputModel read() throws Exception {
        if (!initialized) {
            initializeReader();
        }
        
        if (resultSet != null && resultSet.next()) {
            return mapResultSetToModel();
        }
        
        // 读取完成，清理资源
        cleanup();
        return null;
    }
    
    /**
     * 初始化读取器资源
     */
    private void initializeReader() throws Exception {
        try {
            // 获取数据源连接
            DataSource dataSource = dataSourceService.getDataSourceEntityById(dataSourceId);
            dataSource.setPassword(encryptionService.decryptSingle(dataSource.getPassword()));
            connection = getConnection(dataSource);
            
            // 设置连接参数优化大批量读取
            connection.setAutoCommit(false);
            connection.setReadOnly(true);
            
            // 创建PreparedStatement并设置fetch size
            statement = connection.prepareStatement(sql, 
                    ResultSet.TYPE_FORWARD_ONLY, 
                    ResultSet.CONCUR_READ_ONLY);
            statement.setFetchSize(config.getFetchSize());
            
            // 执行查询
            log.info("开始执行SQL查询，fetch size: {}", config.getFetchSize());
            resultSet = statement.executeQuery();
            
            // 获取元数据
            metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            columnNames = new String[columnCount];
            
            for (int i = 1; i <= columnCount; i++) {
                columnNames[i - 1] = metaData.getColumnLabel(i);
            }
            
            log.info("查询初始化完成，列数: {}", columnCount);
            initialized = true;
            
        } catch (Exception e) {
            log.error("初始化游标读取器失败", e);
            cleanup();
            throw e;
        }
    }
    
    /**
     * 将ResultSet映射为CsvOutputModel
     */
    private CsvOutputModel mapResultSetToModel() throws SQLException {
        CsvOutputModel model = new CsvOutputModel();
        Map<String, Object> data = new LinkedHashMap<>();
        
        for (int i = 0; i < columnNames.length; i++) {
            String columnName = columnNames[i];
            Object value = resultSet.getObject(i + 1);
            
            // 处理null值
            if (value == null) {
                value = "";
            }
            
            data.put(columnName, value);
        }
        
        model.setData(data);
        model.setRowNumber(readCount.incrementAndGet());
        
        // 每读取一定数量记录输出日志
        if (readCount.get() % 10000 == 0) {
            log.debug("已读取记录数: {}", readCount.get());
        }
        
        return model;
    }
    
    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (resultSet != null) {
                resultSet.close();
                resultSet = null;
            }
            if (statement != null) {
                statement.close();
                statement = null;
            }
            if (connection != null) {
                connection.close();
                connection = null;
            }
            log.info("游标读取器资源清理完成，总读取记录数: {}", readCount.get());
        } catch (SQLException e) {
            log.error("清理游标读取器资源失败", e);
        }
    }
    
    /**
     * 获取已读取记录数
     */
    public long getReadCount() {
        return readCount.get();
    }
    
    /**
     * 重置读取器状态
     */
    public void reset() {
        cleanup();
        readCount.set(0);
        initialized = false;
    }
}
