package com.chic.dea.domain.batch;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.model.CsvOutputModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 并发文件写入器
 * 支持异步并发写入多个文件
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class ConcurrentFileWriter {
    
    @Autowired
    private BatchExportConfig config;
    
    private final ExecutorService writeExecutor;
    private final AtomicLong totalWrittenRecords = new AtomicLong(0);
    private final AtomicLong totalWrittenFiles = new AtomicLong(0);
    
    public ConcurrentFileWriter() {
        // 根据配置创建写入线程池，默认3个线程
        int threadCount = 3; // 将在@PostConstruct中从config获取
        this.writeExecutor = Executors.newFixedThreadPool(threadCount, r -> {
            Thread thread = new Thread(r, "csv-writer-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
    }
    
    /**
     * 异步写入CSV文件
     * 
     * @param filePath 文件路径
     * @param data 数据列表
     * @param writeHeader 是否写入表头
     * @param columnHeaders 列头数组
     * @return 异步执行结果
     */
    public CompletableFuture<Void> writeAsync(String filePath, 
                                            List<CsvOutputModel> data, 
                                            boolean writeHeader,
                                            String[] columnHeaders) {
        return CompletableFuture.runAsync(() -> {
            try {
                writeCsvFile(filePath, data, writeHeader, columnHeaders);
                
                // 更新统计信息
                totalWrittenRecords.addAndGet(data.size());
                totalWrittenFiles.incrementAndGet();
                
                log.debug("异步写入完成: {} - {} 条记录", filePath, data.size());
                
            } catch (Exception e) {
                log.error("异步写入失败: {}", filePath, e);
                throw new RuntimeException("文件写入失败: " + filePath, e);
            }
        }, writeExecutor);
    }
    
    /**
     * 同步写入CSV文件
     * 
     * @param filePath 文件路径
     * @param data 数据列表
     * @param writeHeader 是否写入表头
     * @param columnHeaders 列头数组
     */
    public void writeSync(String filePath, 
                         List<CsvOutputModel> data, 
                         boolean writeHeader,
                         String[] columnHeaders) throws IOException {
        writeCsvFile(filePath, data, writeHeader, columnHeaders);
        
        // 更新统计信息
        totalWrittenRecords.addAndGet(data.size());
        totalWrittenFiles.incrementAndGet();
        
        log.debug("同步写入完成: {} - {} 条记录", filePath, data.size());
    }
    
    /**
     * 写入CSV文件的核心方法
     */
    private void writeCsvFile(String filePath, 
                             List<CsvOutputModel> data, 
                             boolean writeHeader,
                             String[] columnHeaders) throws IOException {
        
        if (data == null || data.isEmpty()) {
            log.warn("数据为空，跳过文件写入: {}", filePath);
            return;
        }
        
        try (FileWriter writer = new FileWriter(filePath, true)) { // 追加模式
            
            // 写入表头
            if (writeHeader && columnHeaders != null && columnHeaders.length > 0) {
                writer.write(String.join(",", columnHeaders));
                writer.write("\n");
            }
            
            // 写入数据行
            for (CsvOutputModel item : data) {
                if (item == null || item.getData() == null) {
                    continue;
                }
                
                List<String> values = new ArrayList<>();
                for (String header : columnHeaders) {
                    Object value = item.getFieldValue(header);
                    String stringValue = formatCsvValue(value);
                    values.add(stringValue);
                }
                
                writer.write(String.join(",", values));
                writer.write("\n");
            }
            
            writer.flush();
        }
    }
    
    /**
     * 格式化CSV值，处理特殊字符
     */
    private String formatCsvValue(Object value) {
        if (value == null) {
            return "";
        }
        
        String stringValue = value.toString();
        
        // 如果包含逗号、双引号或换行符，需要用双引号包围并转义
        if (stringValue.contains(",") || stringValue.contains("\"") || 
            stringValue.contains("\n") || stringValue.contains("\r")) {
            
            // 转义双引号
            stringValue = stringValue.replace("\"", "\"\"");
            
            // 用双引号包围
            stringValue = "\"" + stringValue + "\"";
        }
        
        return stringValue;
    }
    
    /**
     * 批量异步写入多个文件
     */
    public CompletableFuture<Void> writeBatchAsync(List<FileWriteTask> writeTasks) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (FileWriteTask task : writeTasks) {
            CompletableFuture<Void> future = writeAsync(
                task.getFilePath(), 
                task.getData(), 
                task.isWriteHeader(),
                task.getColumnHeaders()
            );
            futures.add(future);
        }
        
        // 等待所有写入任务完成
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }
    
    /**
     * 获取写入统计信息
     */
    public String getWriteStatistics() {
        return String.format("写入统计 - 总记录数: %d, 总文件数: %d, 平均每文件记录数: %.2f",
                totalWrittenRecords.get(),
                totalWrittenFiles.get(),
                totalWrittenFiles.get() > 0 ? 
                    (double) totalWrittenRecords.get() / totalWrittenFiles.get() : 0.0);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalWrittenRecords.set(0);
        totalWrittenFiles.set(0);
        log.info("写入统计信息已重置");
    }
    
    /**
     * 检查线程池状态
     */
    public boolean isHealthy() {
        return !writeExecutor.isShutdown() && !writeExecutor.isTerminated();
    }
    
    /**
     * 获取待处理任务数量
     */
    public int getPendingTaskCount() {
        if (writeExecutor instanceof java.util.concurrent.ThreadPoolExecutor) {
            java.util.concurrent.ThreadPoolExecutor executor = 
                (java.util.concurrent.ThreadPoolExecutor) writeExecutor;
            return (int) executor.getTaskCount() - (int) executor.getCompletedTaskCount();
        }
        return 0;
    }
    
    @PreDestroy
    public void cleanup() {
        log.info("开始清理并发文件写入器资源");
        
        writeExecutor.shutdown();
        try {
            if (!writeExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                log.warn("写入线程池未能在30秒内正常关闭，强制关闭");
                writeExecutor.shutdownNow();
                
                if (!writeExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.error("写入线程池强制关闭失败");
                }
            }
        } catch (InterruptedException e) {
            log.warn("等待线程池关闭时被中断");
            writeExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        log.info("并发文件写入器资源清理完成");
        log.info("最终统计: {}", getWriteStatistics());
    }
    
    /**
     * 文件写入任务
     */
    public static class FileWriteTask {
        private final String filePath;
        private final List<CsvOutputModel> data;
        private final boolean writeHeader;
        private final String[] columnHeaders;
        
        public FileWriteTask(String filePath, List<CsvOutputModel> data, 
                           boolean writeHeader, String[] columnHeaders) {
            this.filePath = filePath;
            this.data = data;
            this.writeHeader = writeHeader;
            this.columnHeaders = columnHeaders;
        }
        
        // Getters
        public String getFilePath() { return filePath; }
        public List<CsvOutputModel> getData() { return data; }
        public boolean isWriteHeader() { return writeHeader; }
        public String[] getColumnHeaders() { return columnHeaders; }
    }
}
