package com.chic.dea.domain.batch;

import com.chic.dea.domain.model.CsvOutputModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 数据处理器
 * 对读取的数据进行清洗和格式化
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class DataProcessor implements ItemProcessor<CsvOutputModel, CsvOutputModel> {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public CsvOutputModel process(CsvOutputModel item) throws Exception {
        if (item == null) {
            return null;
        }
        
        // 创建处理后的模型
        CsvOutputModel processedItem = new CsvOutputModel();
        processedItem.setRowNumber(item.getRowNumber());
        
        // 处理每个字段
        Map<String, Object> originalData = item.getData();
        for (Map.Entry<String, Object> entry : originalData.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            
            // 数据清洗和格式化
            Object processedValue = processFieldValue(fieldValue);
            processedItem.addField(fieldName, processedValue);
        }
        
        return processedItem;
    }
    
    /**
     * 处理字段值
     */
    private Object processFieldValue(Object value) {
        if (value == null) {
            return "";
        }
        
        // 处理时间类型
        if (value instanceof Timestamp) {
            Timestamp timestamp = (Timestamp) value;
            return timestamp.toLocalDateTime().format(DATE_TIME_FORMATTER);
        }
        
        if (value instanceof java.sql.Date) {
            java.sql.Date date = (java.sql.Date) value;
            return date.toLocalDate().toString();
        }
        
        if (value instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime) value;
            return dateTime.format(DATE_TIME_FORMATTER);
        }
        
        // 处理数值类型
        if (value instanceof BigDecimal) {
            BigDecimal decimal = (BigDecimal) value;
            // 去除末尾的零
            return decimal.stripTrailingZeros().toPlainString();
        }
        
        if (value instanceof Double) {
            Double doubleValue = (Double) value;
            // 避免科学计数法
            if (doubleValue.isInfinite() || doubleValue.isNaN()) {
                return "";
            }
            return BigDecimal.valueOf(doubleValue).stripTrailingZeros().toPlainString();
        }
        
        if (value instanceof Float) {
            Float floatValue = (Float) value;
            if (floatValue.isInfinite() || floatValue.isNaN()) {
                return "";
            }
            return BigDecimal.valueOf(floatValue.doubleValue()).stripTrailingZeros().toPlainString();
        }
        
        // 处理字符串类型
        if (value instanceof String) {
            String stringValue = (String) value;
            // 清理特殊字符
            return cleanStringValue(stringValue);
        }
        
        // 处理布尔类型
        if (value instanceof Boolean) {
            Boolean boolValue = (Boolean) value;
            return boolValue ? "1" : "0";
        }
        
        // 其他类型直接转换为字符串
        return value.toString();
    }
    
    /**
     * 清理字符串值
     */
    private String cleanStringValue(String value) {
        if (value == null) {
            return "";
        }
        
        // 移除换行符和制表符，避免CSV格式问题
        String cleaned = value.replace("\n", " ")
                              .replace("\r", " ")
                              .replace("\t", " ");
        
        // 移除前后空格
        cleaned = cleaned.trim();
        
        // 如果包含逗号或双引号，需要特殊处理
        if (cleaned.contains(",") || cleaned.contains("\"")) {
            // 转义双引号
            cleaned = cleaned.replace("\"", "\"\"");
            // 用双引号包围
            cleaned = "\"" + cleaned + "\"";
        }
        
        return cleaned;
    }
}
