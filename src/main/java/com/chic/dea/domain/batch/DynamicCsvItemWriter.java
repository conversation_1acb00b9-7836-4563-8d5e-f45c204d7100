package com.chic.dea.domain.batch;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.model.CsvOutputModel;
import com.chic.dea.domain.service.FileManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态分文件CSV写入器
 * 支持根据记录数自动切换文件
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class DynamicCsvItemWriter implements ItemWriter<CsvOutputModel> {
    
    @Autowired
    private FileManager fileManager;
    
    @Autowired
    private BatchExportConfig config;
    
    private final Map<String, FileBuffer> fileBuffers = new ConcurrentHashMap<>();
    private final Object writeLock = new Object();
    private String[] columnHeaders;
    private boolean headersInitialized = false;
    
    @Override
    public void write(List<? extends CsvOutputModel> items) throws Exception {
        if (items == null || items.isEmpty()) {
            return;
        }
        
        synchronized (writeLock) {
            // 初始化列头
            if (!headersInitialized && !items.isEmpty()) {
                initializeHeaders(items.get(0));
            }
            
            processItems(new ArrayList<>(items));
        }
    }
    
    /**
     * 初始化列头
     */
    private void initializeHeaders(CsvOutputModel firstItem) {
        if (firstItem != null && firstItem.getData() != null) {
            columnHeaders = firstItem.getFieldNames();
            headersInitialized = true;
            log.info("初始化CSV列头，列数: {}", columnHeaders.length);
        }
    }
    
    /**
     * 处理数据项
     */
    private void processItems(List<CsvOutputModel> items) throws Exception {
        String currentFile = fileManager.getCurrentFilePath();
        FileBuffer currentBuffer = getOrCreateBuffer(currentFile);
        
        for (CsvOutputModel item : items) {
            // 检查是否需要切换文件
            if (fileManager.shouldSwitchFile(fileManager.getCurrentFileRecords())) {
                // 写入当前缓冲区数据
                flushBuffer(currentFile, currentBuffer);
                
                // 切换到新文件
                currentFile = fileManager.switchToNewFile();
                currentBuffer = getOrCreateBuffer(currentFile);
            }
            
            // 添加数据到缓冲区
            currentBuffer.addData(item);
            fileManager.incrementRecords(1);
            
            // 当缓冲区达到一定大小时，写入文件
            if (currentBuffer.shouldFlush(config.getBufferFlushThreshold())) {
                flushBuffer(currentFile, currentBuffer);
            }
        }
        
        // 更新缓冲区
        fileBuffers.put(currentFile, currentBuffer);
    }
    
    /**
     * 获取或创建文件缓冲区
     */
    private FileBuffer getOrCreateBuffer(String filePath) {
        return fileBuffers.computeIfAbsent(filePath, k -> new FileBuffer());
    }
    
    /**
     * 刷新缓冲区到文件
     */
    private void flushBuffer(String filePath, FileBuffer buffer) throws Exception {
        if (buffer.isEmpty()) {
            return;
        }
        
        try {
            File file = new File(filePath);
            boolean writeHeader = !file.exists();
            
            // 使用CSV格式写入（而不是Excel格式）
            writeCsvFile(filePath, buffer.getData(), writeHeader);
            
            buffer.clear();
            log.debug("写入文件：{}，记录数：{}", filePath, buffer.size());
            
        } catch (Exception e) {
            log.error("写入文件失败：{}", filePath, e);
            throw new RuntimeException("文件写入异常", e);
        }
    }
    
    /**
     * 写入CSV文件
     */
    private void writeCsvFile(String filePath, List<CsvOutputModel> data, boolean writeHeader) throws IOException {
        try (FileWriter writer = new FileWriter(filePath, true)) { // 追加模式
            
            // 写入表头
            if (writeHeader && columnHeaders != null) {
                writer.write(String.join(",", columnHeaders));
                writer.write("\n");
            }
            
            // 写入数据行
            for (CsvOutputModel item : data) {
                List<String> values = new ArrayList<>();
                for (String header : columnHeaders) {
                    Object value = item.getFieldValue(header);
                    values.add(value != null ? value.toString() : "");
                }
                writer.write(String.join(",", values));
                writer.write("\n");
            }
            
            writer.flush();
        }
    }
    
    /**
     * 刷新所有缓冲区
     */
    public void flushAllBuffers() throws Exception {
        synchronized (writeLock) {
            for (Map.Entry<String, FileBuffer> entry : fileBuffers.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    flushBuffer(entry.getKey(), entry.getValue());
                }
            }
            fileBuffers.clear();
            log.info("所有缓冲区数据已写入完成");
        }
    }
    
    /**
     * 重置写入器状态
     */
    public void reset() {
        synchronized (writeLock) {
            fileBuffers.clear();
            columnHeaders = null;
            headersInitialized = false;
            log.info("动态CSV写入器已重置");
        }
    }
    
    /**
     * 文件缓冲区类
     */
    private static class FileBuffer {
        private final List<CsvOutputModel> data = new ArrayList<>();
        
        public void addData(CsvOutputModel item) {
            data.add(item);
        }
        
        public List<CsvOutputModel> getData() {
            return new ArrayList<>(data);
        }
        
        public void clear() {
            data.clear();
        }
        
        public boolean isEmpty() {
            return data.isEmpty();
        }
        
        public int size() {
            return data.size();
        }
        
        public boolean shouldFlush(int threshold) {
            return data.size() >= threshold;
        }
    }
}
