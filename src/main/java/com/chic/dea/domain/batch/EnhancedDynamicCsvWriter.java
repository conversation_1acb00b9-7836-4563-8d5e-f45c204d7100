package com.chic.dea.domain.batch;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.model.CsvOutputModel;
import com.chic.dea.domain.service.FileManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 增强版动态分文件CSV写入器
 * 集成内存监控、并发写入和智能缓冲管理
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class EnhancedDynamicCsvWriter implements ItemWriter<CsvOutputModel> {
    
    @Autowired
    private FileManager fileManager;
    
    @Autowired
    private ConcurrentFileWriter concurrentWriter;
    
    @Autowired
    private BatchExportConfig config;
    
    @Autowired
    private MemoryAwareWriter memoryAwareWriter;
    
    // 文件级别的缓冲区管理
    private final Map<String, FileBuffer> fileBuffers = new ConcurrentHashMap<>();
    private final Object writeLock = new Object();
    private String[] columnHeaders;
    private boolean headersInitialized = false;
    
    @Override
    public void write(List<? extends CsvOutputModel> items) throws Exception {
        if (items == null || items.isEmpty()) {
            return;
        }
        
        synchronized (writeLock) {
            // 初始化列头
            if (!headersInitialized && !items.isEmpty()) {
                initializeHeaders(items.get(0));
            }
            
            // 检查内存使用情况
            if (memoryAwareWriter.needsMemoryOptimization()) {
                log.debug("内存使用率较高，启用内存优化模式");
                processItemsWithMemoryOptimization(new ArrayList<>(items));
            } else {
                processItemsNormally(new ArrayList<>(items));
            }
        }
    }
    
    /**
     * 初始化列头
     */
    private void initializeHeaders(CsvOutputModel firstItem) {
        if (firstItem != null && firstItem.getData() != null) {
            columnHeaders = firstItem.getFieldNames();
            headersInitialized = true;
            log.info("初始化CSV列头，列数: {}", columnHeaders.length);
        }
    }
    
    /**
     * 正常模式处理数据项
     */
    private void processItemsNormally(List<CsvOutputModel> items) throws Exception {
        String currentFile = fileManager.getCurrentFilePath();
        FileBuffer currentBuffer = getOrCreateBuffer(currentFile);
        
        for (CsvOutputModel item : items) {
            // 检查是否需要切换文件
            if (fileManager.shouldSwitchFile(fileManager.getCurrentFileRecords())) {
                // 异步写入当前缓冲区并清理
                asyncFlushBuffer(currentFile, currentBuffer);
                
                // 切换到新文件
                currentFile = fileManager.switchToNewFile();
                currentBuffer = getOrCreateBuffer(currentFile);
            }
            
            // 添加数据到缓冲区
            currentBuffer.addData(item);
            fileManager.incrementRecords(1);
            
            // 缓冲区满时异步写入
            if (currentBuffer.shouldFlush(config.getBufferFlushThreshold())) {
                asyncFlushBuffer(currentFile, currentBuffer);
            }
        }
        
        // 更新缓冲区
        fileBuffers.put(currentFile, currentBuffer);
    }
    
    /**
     * 内存优化模式处理数据项
     */
    private void processItemsWithMemoryOptimization(List<CsvOutputModel> items) throws Exception {
        // 使用较小的批次大小
        int optimizedBatchSize = Math.min(config.getBufferFlushThreshold() / 2, 500);
        
        for (int i = 0; i < items.size(); i += optimizedBatchSize) {
            int endIndex = Math.min(i + optimizedBatchSize, items.size());
            List<CsvOutputModel> batch = items.subList(i, endIndex);
            
            processItemsNormally(batch);
            
            // 强制刷新缓冲区以释放内存
            flushAllBuffersSync();
            
            // 短暂暂停让GC有机会运行
            if (memoryAwareWriter.needsMemoryOptimization()) {
                Thread.sleep(50);
            }
        }
    }
    
    /**
     * 获取或创建文件缓冲区
     */
    private FileBuffer getOrCreateBuffer(String filePath) {
        return fileBuffers.computeIfAbsent(filePath, k -> new FileBuffer());
    }
    
    /**
     * 异步刷新缓冲区到文件
     */
    private void asyncFlushBuffer(String filePath, FileBuffer buffer) {
        if (buffer.isEmpty()) {
            return;
        }
        
        List<CsvOutputModel> dataToWrite = new ArrayList<>(buffer.getData());
        buffer.clear();
        
        File file = new File(filePath);
        boolean writeHeader = !file.exists();
        
        // 异步写入
        CompletableFuture<Void> future = concurrentWriter.writeAsync(
            filePath, dataToWrite, writeHeader, columnHeaders);
        
        future.exceptionally(throwable -> {
            log.error("异步写入失败: {}", filePath, throwable);
            return null;
        });
    }
    
    /**
     * 同步刷新缓冲区到文件
     */
    private void flushBufferSync(String filePath, FileBuffer buffer) throws Exception {
        if (buffer.isEmpty()) {
            return;
        }
        
        try {
            File file = new File(filePath);
            boolean writeHeader = !file.exists();
            
            concurrentWriter.writeSync(filePath, buffer.getData(), writeHeader, columnHeaders);
            
            buffer.clear();
            log.debug("同步写入文件：{}，记录数：{}", filePath, buffer.size());
            
        } catch (Exception e) {
            log.error("同步写入文件失败：{}", filePath, e);
            throw new RuntimeException("文件写入异常", e);
        }
    }
    
    /**
     * 刷新所有缓冲区（异步）
     */
    public void flushAllBuffers() throws Exception {
        synchronized (writeLock) {
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (Map.Entry<String, FileBuffer> entry : fileBuffers.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    String filePath = entry.getKey();
                    FileBuffer buffer = entry.getValue();
                    
                    List<CsvOutputModel> dataToWrite = new ArrayList<>(buffer.getData());
                    buffer.clear();
                    
                    File file = new File(filePath);
                    boolean writeHeader = !file.exists();
                    
                    CompletableFuture<Void> future = concurrentWriter.writeAsync(
                        filePath, dataToWrite, writeHeader, columnHeaders);
                    futures.add(future);
                }
            }
            
            // 等待所有异步写入完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            fileBuffers.clear();
            log.info("所有缓冲区数据已异步写入完成");
        }
    }
    
    /**
     * 同步刷新所有缓冲区
     */
    public void flushAllBuffersSync() throws Exception {
        synchronized (writeLock) {
            for (Map.Entry<String, FileBuffer> entry : fileBuffers.entrySet()) {
                if (!entry.getValue().isEmpty()) {
                    flushBufferSync(entry.getKey(), entry.getValue());
                }
            }
            fileBuffers.clear();
            log.debug("所有缓冲区数据已同步写入完成");
        }
    }
    
    /**
     * 重置写入器状态
     */
    public void reset() {
        synchronized (writeLock) {
            fileBuffers.clear();
            columnHeaders = null;
            headersInitialized = false;
            concurrentWriter.resetStatistics();
            log.info("增强版动态CSV写入器已重置");
        }
    }
    
    /**
     * 作业完成时的清理工作
     */
    @EventListener
    public void handleJobCompletion(JobExecution jobExecution) {
        if (jobExecution.getStatus() == BatchStatus.COMPLETED || 
            jobExecution.getStatus() == BatchStatus.FAILED) {
            
            try {
                flushAllBuffers();
                generateSummaryReport(jobExecution);
            } catch (Exception e) {
                log.error("作业完成后清理失败", e);
            }
        }
    }
    
    /**
     * 生成摘要报告
     */
    private void generateSummaryReport(JobExecution jobExecution) {
        log.info("=== 数据提取完成报告 ===");
        
        long totalRecords = jobExecution.getStepExecutions().stream()
                .mapToLong(StepExecution::getWriteCount)
                .sum();
        
        int generatedFiles = fileManager.getGeneratedFileCount();
        
        log.info("总处理记录数: {}", totalRecords);
        log.info("生成文件数量: {}", generatedFiles);
        
        if (generatedFiles > 0) {
            log.info("平均每文件记录数: {}", totalRecords / generatedFiles);
        }
        
        log.info("并发写入统计: {}", concurrentWriter.getWriteStatistics());
        log.info("内存使用统计: {}", memoryAwareWriter.getMemoryStatistics());
    }
    
    /**
     * 文件缓冲区类
     */
    private static class FileBuffer {
        private final List<CsvOutputModel> data = new ArrayList<>();
        
        public void addData(CsvOutputModel item) {
            data.add(item);
        }
        
        public List<CsvOutputModel> getData() {
            return new ArrayList<>(data);
        }
        
        public void clear() {
            data.clear();
        }
        
        public boolean isEmpty() {
            return data.isEmpty();
        }
        
        public int size() {
            return data.size();
        }
        
        public boolean shouldFlush(int threshold) {
            return data.size() >= threshold;
        }
    }
}
