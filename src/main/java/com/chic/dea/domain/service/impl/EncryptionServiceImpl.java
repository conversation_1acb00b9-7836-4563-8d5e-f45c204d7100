package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.service.EncryptionService;
import com.chic.dea.infrastructure.general.util.PasswordEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 加密服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EncryptionServiceImpl implements EncryptionService {

    private final PasswordEncryptionUtil passwordEncryptionUtil;

    @Override
    public String encryptSingle(String plainText) {
        log.info("执行单个文本加密");
        
        if (!StringUtils.hasText(plainText)) {
            throw new IllegalArgumentException("明文不能为空");
        }

        try {
            String encryptedText = passwordEncryptionUtil.encrypt(plainText);
            log.info("单个文本加密成功");
            return encryptedText;
        } catch (Exception e) {
            log.error("单个文本加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("加密失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String decryptSingle(String encryptedText) {
        log.info("执行单个文本解密");
        
        if (!StringUtils.hasText(encryptedText)) {
            throw new IllegalArgumentException("密文不能为空");
        }

        try {
            String plainText = passwordEncryptionUtil.decrypt(encryptedText);
            log.info("单个文本解密成功");
            return plainText;
        } catch (Exception e) {
            log.error("单个文本解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }

}
