package com.chic.dea.domain.service;

import com.alibaba.druid.pool.DruidDataSource;
import com.chic.dea.apis.model.vo.DataSourceVO;
import com.chic.dea.domain.database.entity.DataSource;
import com.chic.dea.infrastructure.general.constants.RedisKeyConstants;
import com.chic.dea.infrastructure.general.util.PasswordEncryptionUtil;
import com.chic.dea.infrastructure.sql.SqlBuilder;
import com.chic.dea.infrastructure.sql.SqlBuilderContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 数据源管理器
 * 负责创建、缓存和管理数据源连接池
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSourceManager {

    private final RedisTemplate<Object, Object> redisTemplate;
    private final PasswordEncryptionUtil passwordEncryptionUtil;
    
    // 本地缓存，避免频繁访问Redis
    private final ConcurrentHashMap<Long, DruidDataSource> localCache = new ConcurrentHashMap<>();
    
    // 缓存过期时间（小时）
    private static final int CACHE_EXPIRE_HOURS = 2;
    
    /**
     * 获取数据源连接池
     * 
     * @param dataSource 数据源实体
     * @return DruidDataSource连接池
     */
    public DruidDataSource getDataSource(DataSource dataSource) {
        Long dataSourceId = dataSource.getId();
        
        // 1. 先从本地缓存获取
        DruidDataSource cachedDataSource = localCache.get(dataSourceId);
        if (cachedDataSource != null && !cachedDataSource.isClosed()) {
            log.debug("从本地缓存获取数据源: {}", dataSourceId);
            return cachedDataSource;
        }
        
        // 2. 从Redis缓存获取
        String redisKey = RedisKeyConstants.DATASOURCE_CACHE_PREFIX + dataSourceId;
        try {
            DruidDataSource redisDataSource = (DruidDataSource) redisTemplate.opsForValue().get(redisKey);
            if (redisDataSource != null && !redisDataSource.isClosed()) {
                log.debug("从Redis缓存获取数据源: {}", dataSourceId);
                localCache.put(dataSourceId, redisDataSource);
                return redisDataSource;
            }
        } catch (Exception e) {
            log.warn("从Redis获取数据源缓存失败: {}", e.getMessage());
        }
        
        // 3. 创建新的数据源连接池
        return createAndCacheDataSource(dataSource);
    }
    
    /**
     * 获取JdbcTemplate
     * 
     * @param dataSource 数据源实体
     * @return JdbcTemplate实例
     */
    public JdbcTemplate getJdbcTemplate(DataSource dataSource) {
        DruidDataSource druidDataSource = getDataSource(dataSource);
        return new JdbcTemplate(druidDataSource);
    }
    
    /**
     * 创建并缓存数据源
     * 
     * @param dataSource 数据源实体
     * @return DruidDataSource连接池
     */
    private synchronized DruidDataSource createAndCacheDataSource(DataSource dataSource) {
        Long dataSourceId = dataSource.getId();
        
        // 双重检查，避免重复创建
        DruidDataSource existingDataSource = localCache.get(dataSourceId);
        if (existingDataSource != null && !existingDataSource.isClosed()) {
            return existingDataSource;
        }
        
        try {
            log.info("创建新的数据源连接池: {}", dataSourceId);
            
            // 转换为VO对象
            DataSourceVO dataSourceVO = convertToVO(dataSource);
            
            // 使用SqlBuilder创建数据源
            SqlBuilder sqlBuilder = SqlBuilderContext.getSqlBuilderService(dataSource.getType());
            DruidDataSource druidDataSource = sqlBuilder.buildDataSource(dataSourceVO);
            
            // 缓存到本地
            localCache.put(dataSourceId, druidDataSource);
            
            // 缓存到Redis
            cacheToRedis(dataSourceId, druidDataSource);
            
            log.info("数据源连接池创建成功: {}", dataSourceId);
            return druidDataSource;
            
        } catch (Exception e) {
            log.error("创建数据源连接池失败: {}", dataSourceId, e);
            throw new RuntimeException("创建数据源连接池失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 缓存数据源到Redis
     * 
     * @param dataSourceId 数据源ID
     * @param druidDataSource 数据源连接池
     */
    private void cacheToRedis(Long dataSourceId, DruidDataSource druidDataSource) {
        try {
            String redisKey = RedisKeyConstants.DATASOURCE_CACHE_PREFIX + dataSourceId;
            redisTemplate.opsForValue().set(redisKey, druidDataSource, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("数据源缓存到Redis成功: {}", dataSourceId);
        } catch (Exception e) {
            log.warn("数据源缓存到Redis失败: {}", e.getMessage());
        }
    }
    
    /**
     * 将数据源实体转换为VO
     * 
     * @param dataSource 数据源实体
     * @return DataSourceVO
     */
    private DataSourceVO convertToVO(DataSource dataSource) {
        DataSourceVO vo = new DataSourceVO();
        BeanUtils.copyProperties(dataSource, vo);
        
        // 解密密码
        try {
            String decryptedPassword = passwordEncryptionUtil.decrypt(dataSource.getPassword());
            vo.setPassword(decryptedPassword);
        } catch (Exception e) {
            log.error("解密数据源密码失败: {}", dataSource.getId(), e);
            throw new RuntimeException("解密数据源密码失败", e);
        }
        
        return vo;
    }
    
    /**
     * 移除缓存的数据源
     * 
     * @param dataSourceId 数据源ID
     */
    public void removeDataSource(Long dataSourceId) {
        log.info("移除数据源缓存: {}", dataSourceId);
        
        // 关闭并移除本地缓存
        DruidDataSource localDataSource = localCache.remove(dataSourceId);
        if (localDataSource != null) {
            try {
                localDataSource.close();
            } catch (Exception e) {
                log.warn("关闭数据源连接池失败: {}", e.getMessage());
            }
        }
        
        // 移除Redis缓存
        try {
            String redisKey = RedisKeyConstants.DATASOURCE_CACHE_PREFIX + dataSourceId;
            redisTemplate.delete(redisKey);
        } catch (Exception e) {
            log.warn("移除Redis数据源缓存失败: {}", e.getMessage());
        }
    }
    
    /**
     * 清理所有缓存的数据源
     */
    public void clearAllDataSources() {
        log.info("清理所有数据源缓存");
        
        // 关闭所有本地缓存的数据源
        localCache.values().forEach(dataSource -> {
            try {
                if (!dataSource.isClosed()) {
                    dataSource.close();
                }
            } catch (Exception e) {
                log.warn("关闭数据源连接池失败: {}", e.getMessage());
            }
        });
        localCache.clear();
        
        // 清理Redis缓存（通过模式匹配删除）
        try {
            String pattern = RedisKeyConstants.DATASOURCE_CACHE_PREFIX + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
        } catch (Exception e) {
            log.warn("清理Redis数据源缓存失败: {}", e.getMessage());
        }
    }
    
    /**
     * 测试数据源连接
     * 
     * @param dataSource 数据源实体
     * @return 是否连接成功
     */
    public boolean testConnection(DataSource dataSource) {
        try {
            DruidDataSource druidDataSource = getDataSource(dataSource);
            // 获取连接测试
            druidDataSource.getConnection().close();
            return true;
        } catch (SQLException e) {
            log.error("测试数据源连接失败: {}", dataSource.getId(), e);
            return false;
        }
    }
}
