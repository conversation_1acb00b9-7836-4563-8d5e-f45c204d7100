package com.chic.dea.domain.service.impl;

import com.chic.dea.domain.config.BatchExportConfig;
import com.chic.dea.domain.service.FileManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 文件管理器实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Component
public class FileManagerImpl implements FileManager {
    
    private final BatchExportConfig config;
    private final AtomicInteger currentFileIndex = new AtomicInteger(1);
    private final AtomicInteger currentFileRecords = new AtomicInteger(0);
    private volatile String currentFilePath;
    private volatile String baseTimestamp;
    
    public FileManagerImpl(BatchExportConfig config) {
        this.config = config;
        this.baseTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        this.currentFilePath = generateFilePath(currentFileIndex.get());
    }
    
    @Override
    public String getCurrentFilePath() {
        return currentFilePath;
    }
    
    @Override
    public boolean shouldSwitchFile(int currentRecords) {
        return currentRecords >= config.getMaxRecordsPerFile();
    }
    
    @Override
    public String switchToNewFile() {
        int newIndex = currentFileIndex.incrementAndGet();
        currentFileRecords.set(0);
        currentFilePath = generateFilePath(newIndex);
        
        // 确保目录存在
        ensureDirectoryExists(currentFilePath);
        
        log.info("切换到新文件：{}", currentFilePath);
        return currentFilePath;
    }
    
    @Override
    public int getCurrentFileRecords() {
        return currentFileRecords.get();
    }
    
    @Override
    public void incrementRecords(int count) {
        currentFileRecords.addAndGet(count);
    }
    
    @Override
    public void reset() {
        currentFileIndex.set(1);
        currentFileRecords.set(0);
        baseTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        currentFilePath = generateFilePath(currentFileIndex.get());
        
        // 确保目录存在
        ensureDirectoryExists(currentFilePath);
        
        log.info("文件管理器已重置，当前文件：{}", currentFilePath);
    }
    
    @Override
    public int getGeneratedFileCount() {
        return currentFileIndex.get();
    }
    
    /**
     * 生成文件路径
     */
    private String generateFilePath(int fileIndex) {
        return String.format("%s/%s_%s_part%03d.csv", 
                config.getOutputBasePath(),
                config.getFilePrefix(),
                baseTimestamp,
                fileIndex);
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String filePath) {
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                log.info("创建输出目录：{}", parentDir.getAbsolutePath());
            } else {
                log.warn("创建输出目录失败：{}", parentDir.getAbsolutePath());
            }
        }
    }
}
