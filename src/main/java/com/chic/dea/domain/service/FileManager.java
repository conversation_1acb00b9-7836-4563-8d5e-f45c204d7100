package com.chic.dea.domain.service;

/**
 * 文件管理器接口
 * 负责动态分文件管理
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface FileManager {
    
    /**
     * 获取当前活跃的文件路径
     * 
     * @return 当前文件路径
     */
    String getCurrentFilePath();
    
    /**
     * 检查是否需要切换到新文件
     * 
     * @param currentFileRecords 当前文件记录数
     * @return 是否需要切换
     */
    boolean shouldSwitchFile(int currentFileRecords);
    
    /**
     * 切换到新文件
     * 
     * @return 新文件路径
     */
    String switchToNewFile();
    
    /**
     * 获取当前文件记录数
     * 
     * @return 当前文件记录数
     */
    int getCurrentFileRecords();
    
    /**
     * 增加记录计数
     * 
     * @param count 增加的记录数
     */
    void incrementRecords(int count);
    
    /**
     * 重置文件管理器状态
     */
    void reset();
    
    /**
     * 获取已生成的文件数量
     * 
     * @return 文件数量
     */
    int getGeneratedFileCount();
}
