package com.chic.dea.domain.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chic.dea.apis.model.dto.*;
import com.chic.dea.apis.model.vo.*;
import com.chic.dea.domain.database.entity.*;
import com.chic.dea.domain.database.mapper.*;
import com.chic.dea.domain.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务编辑服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskEditServiceImpl implements TaskEditService {

    private final ExtractionTaskMapper taskMapper;
    private final TaskExecutionQueueMapper queueMapper;
    private final TaskExecutionLogMapper logMapper;
    private final DataSourceService dataSourceService;
    private final SqlValidationService sqlValidationService;
    private final SensitiveFieldService sensitiveFieldService;

    @Override
    public TaskDetailVO getTaskDetailById(Long taskId) {
        log.info("获取任务详情, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }

        TaskDetailVO detailVO = new TaskDetailVO();
        BeanUtils.copyProperties(task, detailVO);
        
        // 设置状态名称
        ExtractionStatus status = task.getExtractionStatusEnum();
        if (status != null) {
            detailVO.setExtractionStatusName(status.getDescription());
        }
        
        // 设置数据源信息
        if (task.getDataSourceId() != null) {
            try {
                DataSourceVO dataSource = dataSourceService.getDataSourceById(task.getDataSourceId());
                detailVO.setDataSourceName(dataSource.getName());
                detailVO.setDataSourceType(dataSource.getType());
            } catch (Exception e) {
                log.warn("获取数据源信息失败, dataSourceId: {}", task.getDataSourceId());
            }
        }
        
        // 设置可读的文件大小和执行时长
        if (task.getFileSize() != null) {
            detailVO.setFileSizeReadable(formatFileSize(task.getFileSize()));
        }
        
        if (task.getExecutionDuration() != null) {
            detailVO.setExecutionDurationReadable(formatDuration(task.getExecutionDuration()));
        }
        
        // 设置操作权限
        detailVO.setEditable(task.isEditable());
        detailVO.setExecutable(task.isExecutable());

        log.info("任务详情获取成功, taskId: {}", taskId);
        return detailVO;
    }

    @Override
    public PageResponse<TaskListVO> getTaskList(TaskQueryRequest request) {
        log.info("分页查询任务列表, 页码: {}, 页大小: {}", request.getPageNum(), request.getPageSize());
        
        Page<TaskListVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        Page<TaskListVO> resultPage = taskMapper.selectTaskPage(page, request);
        
        // 补充额外信息
        for (TaskListVO taskVO : resultPage.getRecords()) {
            // 设置状态名称
            try {
                ExtractionStatus status = ExtractionStatus.valueOf(taskVO.getExtractionStatus());
                taskVO.setExtractionStatusName(status.getDescription());
            } catch (Exception e) {
                taskVO.setExtractionStatusName("未知状态");
            }
            
            // 设置操作权限
            taskVO.setEditable(isTaskEditableByStatus(taskVO.getExtractionStatus()));
            taskVO.setExecutable("APPROVED".equals(taskVO.getExtractionStatus()));
            
            // 计算执行时长
            if (taskVO.getExecutionStartTime() != null && taskVO.getExecutionEndTime() != null) {
                long duration = java.time.Duration.between(
                    taskVO.getExecutionStartTime(), 
                    taskVO.getExecutionEndTime()
                ).toMillis();
                taskVO.setExecutionDuration(duration);
            }
        }
        
        log.info("任务列表查询完成, 总记录数: {}", resultPage.getTotal());
        return PageResponse.of(resultPage.getRecords(), resultPage.getTotal(), 
                             request.getPageNum(), request.getPageSize());
    }

    @Override
    @Transactional
    public void updateTask(Long taskId, TaskUpdateRequest request) {
        log.info("更新任务信息, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        // 检查任务状态是否允许编辑
        if (!task.isEditable()) {
            throw new RuntimeException("当前状态不允许编辑: " + task.getExtractionStatus());
        }
        
        // 保存原始的SQL和数据源ID用于比较
        String originalScript = task.getExtractionScript();
        Long originalDataSourceId = task.getDataSourceId();
        
        // 更新允许修改的字段
        task.setDataSourceId(request.getDataSourceId());
        task.setExtractionScript(request.getExtractionScript());
        task.setCompliancePdfUrl(request.getCompliancePdfUrl());
        
        // 如果SQL或数据源发生变化，需要重新进行合规性检查
        boolean needRevalidation = !originalScript.equals(request.getExtractionScript()) ||
                                 !originalDataSourceId.equals(request.getDataSourceId());
        DataSource dataSource = dataSourceService.getDataSourceEntityById(originalDataSourceId);
        if (needRevalidation) {
            log.info("检测到SQL或数据源变化，进行重新校验");
            
            SqlValidationService.SqlValidationResult validation = sqlValidationService.validateSyntax(request.getExtractionScript(),dataSource.getType());
            if (!validation.isValid()) {
                throw new RuntimeException("SQL语法错误: " + validation.getErrorMessage());
            }
            
            // 进行敏感字段检查




            boolean hasSensitiveFields = true;
            if (hasSensitiveFields) {
                task.setExtractionStatusEnum(ExtractionStatus.PENDING_COMPLIANCE_REVIEW);
                log.info("检测到敏感字段，任务状态设置为待合规审核");
            } else {
                task.setExtractionStatusEnum(ExtractionStatus.PENDING_EXTRACTION);
                log.info("未检测到敏感字段，任务状态设置为待提数");
            }
        }
        
        taskMapper.updateById(task);
        log.info("任务更新成功, taskId: {}", taskId);
    }

    @Override
    public boolean isTaskEditable(Long taskId) {
        ExtractionTask task = taskMapper.selectById(taskId);
        return task != null && task.isEditable();
    }

    @Override
    @Transactional
    public void archiveTask(Long taskId) {
        log.info("归档任务, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        ExtractionStatus status = task.getExtractionStatusEnum();
        if (status != ExtractionStatus.ARCHIVED && status != ExtractionStatus.DATA_GENERATION_FAILED) {
            throw new RuntimeException("只能归档已完成或失败的任务");
        }
        

        task.setExtractionStatusEnum(ExtractionStatus.ARCHIVED);
        taskMapper.updateById(task);
        
        log.info("任务归档成功, taskId: {}", taskId);
    }

    @Override
    @Transactional
    public void deleteTask(Long taskId) {
        log.info("删除任务, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        ExtractionStatus status = task.getExtractionStatusEnum();
        if (status == ExtractionStatus.DATA_GENERATING) {
            throw new RuntimeException("不能删除正在执行的任务");
        }
        
        // 删除相关的队列记录和日志记录
        TaskExecutionQueue queueItem = queueMapper.findByTaskId(taskId);
        if (queueItem != null) {
            queueMapper.deleteById(queueItem.getId());
        }
        
        // 删除执行日志
        List<TaskExecutionLog> logs = logMapper.findByTaskId(taskId);
        if (!logs.isEmpty()) {
            List<Long> logIds = logs.stream().map(TaskExecutionLog::getId).collect(Collectors.toList());
            logMapper.deleteBatchIds(logIds);
        }
        
        // 删除任务
        taskMapper.deleteById(taskId);
        
        log.info("任务删除成功, taskId: {}", taskId);
    }

    @Override
    @Transactional
    public void cancelExecutingTask(Long taskId) {
        log.info("取消执行中任务, taskId: {}", taskId);
        
        ExtractionTask task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        if (task.getExtractionStatusEnum() != ExtractionStatus.DATA_GENERATING) {
            throw new RuntimeException("只能取消正在执行的任务");
        }
        
        // 更新任务状态为失败
        task.setExtractionStatusEnum(ExtractionStatus.DATA_GENERATION_FAILED);
        task.setErrorMessage("任务被用户取消");
        task.setExecutionEndTime(LocalDateTime.now());
        taskMapper.updateById(task);
        
        // 更新队列状态
        TaskExecutionQueue queueItem = queueMapper.findByTaskId(taskId);
        if (queueItem != null) {
            queueItem.setQueueStatusEnum(QueueStatus.FAILED);
            queueMapper.updateById(queueItem);
        }
        
        log.info("任务取消成功, taskId: {}", taskId);
    }

    @Override
    public List<TaskExecutionLogVO> getTaskExecutionLogs(Long taskId) {
        log.info("获取任务执行日志, taskId: {}", taskId);
        
        List<TaskExecutionLog> logs = logMapper.findByTaskId(taskId);
        
        return logs.stream().map(log -> {
            TaskExecutionLogVO logVO = new TaskExecutionLogVO();
            BeanUtils.copyProperties(log, logVO);
            
            // 计算执行时长
            if (log.getExecutionDuration() != null) {
                logVO.setExecutionDuration(log.getExecutionDuration());
            }
            
            return logVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据状态判断任务是否可编辑
     */
    private boolean isTaskEditableByStatus(String status) {
        try {
            ExtractionStatus extractionStatus = ExtractionStatus.valueOf(status);
            return extractionStatus.isEditable();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * 格式化执行时长
     */
    private String formatDuration(Long durationMillis) {
        if (durationMillis == null || durationMillis <= 0) {
            return "0秒";
        }
        
        long seconds = durationMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
