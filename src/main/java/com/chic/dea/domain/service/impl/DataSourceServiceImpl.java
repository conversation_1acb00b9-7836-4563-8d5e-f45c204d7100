package com.chic.dea.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chic.dea.apis.model.dto.*;
import com.chic.dea.apis.model.vo.DataSourceVO;
import com.chic.dea.domain.database.entity.DataSource;
import com.chic.dea.domain.database.mapper.DataSourceMapper;
import com.chic.dea.domain.service.DataSourceService;
import com.chic.dea.infrastructure.general.util.DataSourceConnectionUtil;
import com.chic.dea.infrastructure.general.util.PasswordEncryptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据源服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataSourceServiceImpl implements DataSourceService {

    private final DataSourceMapper dataSourceMapper;
    private final PasswordEncryptionUtil passwordEncryptionUtil;

    @Override
    public List<DataSourceVO> getAllDataSources() {
        List<DataSource> dataSources = dataSourceMapper.selectList(
            new LambdaQueryWrapper<DataSource>()
                .orderByDesc(DataSource::getUpdateTime)
        );
        return dataSources.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public List<DataSourceVO> getEnabledDataSources() {
        List<DataSource> dataSources = dataSourceMapper.findEnabledDataSources();
        return dataSources.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public DataSourceVO getDataSourceById(Long id) {
        DataSource dataSource = getDataSourceEntityById(id);
        return convertToVO(dataSource);
    }

    @Override
    public DataSource getDataSourceEntityById(Long id) {
        DataSource dataSource = dataSourceMapper.selectById(id);
        if (dataSource == null) {
            throw new IllegalArgumentException("数据源不存在，ID: " + id);
        }
        return dataSource;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDataSource(DataSourceCreateRequest request) {
        // 验证数据源名称是否已存在
        if (existsByName(request.getName(), null)) {
            throw new IllegalArgumentException("数据源名称已存在: " + request.getName());
        }

        // 先测试数据源连接
        TestConnectionResponse testResult = testConnectionConfig(request);
        if (!testResult.getSuccess()) {
            throw new IllegalArgumentException("数据源连接测试失败: " + testResult.getErrorMessage());
        }

        // 验证数据源类型
        try {
            DataSourceType.fromCode(request.getType());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("不支持的数据源类型: " + request.getType());
        }

        // 创建数据源实体
        DataSource dataSource = new DataSource();
        BeanUtils.copyProperties(request, dataSource);
        
        // 前端传入的密码已经是加密过的，直接保存
        dataSource.setPassword(request.getPassword());

        // 保存到数据库
        dataSourceMapper.insert(dataSource);
        
        log.info("创建数据源成功，ID: {}, 名称: {}, 连接测试耗时: {}ms", 
            dataSource.getId(), dataSource.getName(), testResult.getDuration());
        return dataSource.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDataSource(Long id, DataSourceUpdateRequest request) {
        // 获取原数据源
        DataSource existingDataSource = getDataSourceEntityById(id);

        // 验证数据源名称是否已存在(排除当前数据源)
        if (StringUtils.hasText(request.getName()) && existsByName(request.getName(), id)) {
            throw new IllegalArgumentException("数据源名称已存在: " + request.getName());
        }

        // 创建测试用的配置对象，合并原有配置和更新配置
        DataSourceCreateRequest testConfig = new DataSourceCreateRequest();
        testConfig.setName(StringUtils.hasText(request.getName()) ? request.getName() : existingDataSource.getName());
        testConfig.setType(StringUtils.hasText(request.getType()) ? request.getType() : existingDataSource.getType());
        testConfig.setUrl(StringUtils.hasText(request.getUrl()) ? request.getUrl() : existingDataSource.getUrl());
        testConfig.setUsername(StringUtils.hasText(request.getUsername()) ? request.getUsername() : existingDataSource.getUsername());
        
        // 密码处理：如果有新密码则解密后使用，否则使用解密后的原密码
        String passwordForTest;
        if (StringUtils.hasText(request.getPassword())) {
            // 前端传入的密码是加密的，需要解密后测试
            passwordForTest = decryptPassword(request.getPassword());
        } else {
            passwordForTest = decryptPassword(existingDataSource.getPassword());
        }
        testConfig.setPassword(passwordForTest);

        // 先测试更新后的数据源连接
        TestConnectionResponse testResult = testConnectionConfig(testConfig);
        if (!testResult.getSuccess()) {
            throw new IllegalArgumentException("数据源连接测试失败: " + testResult.getErrorMessage());
        }

        // 验证数据源类型
        if (StringUtils.hasText(request.getType())) {
            try {
                DataSourceType.fromCode(request.getType());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("不支持的数据源类型: " + request.getType());
            }
        }

        // 更新数据源
        if (StringUtils.hasText(request.getName())) {
            existingDataSource.setName(request.getName());
        }
        if (StringUtils.hasText(request.getType())) {
            existingDataSource.setType(request.getType());
        }
        if (StringUtils.hasText(request.getUrl())) {
            existingDataSource.setUrl(request.getUrl());
        }
        if (StringUtils.hasText(request.getUsername())) {
            existingDataSource.setUsername(request.getUsername());
        }
        if (StringUtils.hasText(request.getPassword())) {
            // 前端传入的密码已经是加密过的，直接保存
            existingDataSource.setPassword(request.getPassword());
        }
        if (request.getStatus() != null) {
            existingDataSource.setStatus(request.getStatus());
        }

        // 保存更新
        dataSourceMapper.updateById(existingDataSource);
        
        log.info("更新数据源成功，ID: {}, 名称: {}, 连接测试耗时: {}ms", 
            id, existingDataSource.getName(), testResult.getDuration());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataSource(Long id) {
        // 检查数据源是否存在
        DataSource dataSource = getDataSourceEntityById(id);

        // TODO: 检查是否有提数任务正在使用此数据源
        // 这里可以添加业务校验逻辑

        // 删除数据源
        dataSourceMapper.deleteById(id);
        
        log.info("删除数据源成功，ID: {}, 名称: {}", id, dataSource.getName());
    }

    @Override
    public TestConnectionResponse testConnection(Long id) {
        DataSource dataSource = getDataSourceEntityById(id);
        
        // 解密密码
        String decryptedPassword = decryptPassword(dataSource.getPassword());
        dataSource.setPassword(decryptedPassword);
        
        return DataSourceConnectionUtil.testConnection(dataSource);
    }

    @Override
    public TestConnectionResponse testConnectionConfig(DataSourceCreateRequest request) {
        // 验证数据源类型
        try {
            DataSourceType.fromCode(request.getType());
        } catch (IllegalArgumentException e) {
            return TestConnectionResponse.failure("不支持的数据源类型: " + request.getType(), 0);
        }

        // 创建临时数据源对象用于测试
        DataSource tempDataSource = new DataSource();
        BeanUtils.copyProperties(request, tempDataSource);
        // 前端传入的密码是加密的，需要解密后测试
        tempDataSource.setPassword(decryptPassword(request.getPassword()));
        
        return DataSourceConnectionUtil.testConnection(tempDataSource);
    }

    @Override
    public List<DataSourceVO> getDataSourcesByType(String type) {
        List<DataSource> dataSources = dataSourceMapper.findByType(type);
        return dataSources.stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());
    }

    @Override
    public boolean existsByName(String name, Long excludeId) {
        if (excludeId != null) {
            return dataSourceMapper.countByNameExcludeId(name, excludeId) > 0;
        } else {
            return dataSourceMapper.countByName(name) > 0;
        }
    }

    /**
     * 将数据源实体转换为VO
     */
    private DataSourceVO convertToVO(DataSource dataSource) {
        DataSourceVO vo = new DataSourceVO();
        BeanUtils.copyProperties(dataSource, vo);
        // 设置状态名称
        vo.setStatusName(dataSource.getStatus() == 1 ? "启用" : "禁用");
        return vo;
    }

    /**
     * 解密密码
     * 
     * @param encryptedPassword 加密的密码
     * @return 解密后的密码
     */
    private String decryptPassword(String encryptedPassword) {
        try {
            return passwordEncryptionUtil.decrypt(encryptedPassword);
        } catch (Exception e) {
            log.warn("密码解密失败，可能是未加密的明文密码或密钥不匹配: {}", e.getMessage());
            // 为了向后兼容，如果解密失败，尝试直接返回原值
            return encryptedPassword;
        }
    }
}
