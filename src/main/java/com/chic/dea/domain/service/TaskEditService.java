package com.chic.dea.domain.service;

import com.chic.dea.apis.model.dto.*;
import com.chic.dea.apis.model.vo.*;

/**
 * 任务编辑服务接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface TaskEditService {

    /**
     * 根据ID获取任务详情
     * 
     * @param taskId 任务ID
     * @return 任务详情VO
     */
    TaskDetailVO getTaskDetailById(Long taskId);

    /**
     * 分页查询任务列表
     * 
     * @param request 查询请求
     * @return 分页任务列表
     */
    PageResponse<TaskListVO> getTaskList(TaskQueryRequest request);

    /**
     * 更新任务信息
     * 
     * @param taskId 任务ID
     * @param request 更新请求
     */
    void updateTask(Long taskId, TaskUpdateRequest request);

    /**
     * 检查任务是否可以编辑
     * 
     * @param taskId 任务ID
     * @return 是否可编辑
     */
    boolean isTaskEditable(Long taskId);

    /**
     * 归档任务
     * 
     * @param taskId 任务ID
     */
    void archiveTask(Long taskId);

    /**
     * 删除任务（软删除）
     * 
     * @param taskId 任务ID
     */
    void deleteTask(Long taskId);

    /**
     * 取消执行中的任务
     * 
     * @param taskId 任务ID
     */
    void cancelExecutingTask(Long taskId);

    /**
     * 获取任务执行日志
     * 
     * @param taskId 任务ID
     * @return 执行日志列表
     */
    java.util.List<TaskExecutionLogVO> getTaskExecutionLogs(Long taskId);

    /**
     * 任务执行日志VO
     */
    class TaskExecutionLogVO {
        private Long id;
        private Long taskId;
        private String stepName;
        private String status;
        private java.time.LocalDateTime startTime;
        private java.time.LocalDateTime endTime;
        private String errorMessage;
        private Long executionDuration;

        // getters and setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }
        
        public String getStepName() { return stepName; }
        public void setStepName(String stepName) { this.stepName = stepName; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public java.time.LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(java.time.LocalDateTime startTime) { this.startTime = startTime; }
        
        public java.time.LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(java.time.LocalDateTime endTime) { this.endTime = endTime; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public Long getExecutionDuration() { return executionDuration; }
        public void setExecutionDuration(Long executionDuration) { this.executionDuration = executionDuration; }
    }
}
