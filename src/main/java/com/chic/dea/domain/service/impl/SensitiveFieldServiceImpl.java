package com.chic.dea.domain.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chic.dea.apis.model.dto.LineageTableFieldsVO;
import com.chic.dea.domain.database.entity.ColumnClassify;
import com.chic.dea.domain.database.mapper.SensitiveFieldMapper;
import com.chic.dea.domain.service.SensitiveFieldService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 敏感字段检查服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class SensitiveFieldServiceImpl implements SensitiveFieldService {

    @Autowired
    private SensitiveFieldMapper sensitiveFieldMapper;

    @DS("tianyu")
    @Override
    public List<SensitiveFieldCheckResult> checkSensitiveFields(List<LineageTableFieldsVO> list) {
        log.info("查询敏感字段参数: {}", list);

        if (CollectionUtils.isEmpty(list)) {
            log.info("输入的表字段列表为空，返回空结果");
            return new ArrayList<>();
        }

        List<SensitiveFieldCheckResult> results = new ArrayList<>();

        // 遍历每个表及其字段列表
        for (LineageTableFieldsVO tableFields : list) {
            String tableName = tableFields.getTableName();
            List<String> fieldList = tableFields.getFieldList();

            if (tableName == null || CollectionUtils.isEmpty(fieldList)) {
                log.warn("表名为空或字段列表为空，跳过: tableName={}, fieldList={}", tableName, fieldList);
                continue;
            }

            try {
                // 查询该表的敏感字段
                List<ColumnClassify> sensitiveFields = sensitiveFieldMapper.findSensitiveFields(tableName, fieldList);
                log.info("表 {} 查询到 {} 个敏感字段", tableName, sensitiveFields.size());

                // 转换为结果对象
                for (ColumnClassify columnClassify : sensitiveFields) {
                    SensitiveFieldCheckResult result = new SensitiveFieldCheckResult(
                        columnClassify.getTableName(),
                        columnClassify.getColumnName(),
                        columnClassify.getSensitiveFlag()
                    );
                    results.add(result);
                    log.debug("发现敏感字段: 表={}, 字段={}, 敏感标识={}",
                        columnClassify.getTableName(),
                        columnClassify.getColumnName(),
                        columnClassify.getSensitiveFlag());
                }
            } catch (Exception e) {
                log.error("查询表 {} 的敏感字段时发生异常: {}", tableName, e.getMessage(), e);
                // 继续处理其他表，不中断整个流程
                throw new RuntimeException("查询敏感字段失败: " + e.getMessage(), e);
            }
        }

        log.info("敏感字段检查完成，共发现 {} 个敏感字段", results.size());
        return results;
    }








}
