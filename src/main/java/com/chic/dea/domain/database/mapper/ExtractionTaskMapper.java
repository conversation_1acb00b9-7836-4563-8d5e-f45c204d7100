package com.chic.dea.domain.database.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chic.dea.apis.model.dto.TaskQueryRequest;
import com.chic.dea.apis.model.vo.TaskListVO;
import com.chic.dea.domain.database.entity.ExtractionTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 提数任务Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Mapper
public interface ExtractionTaskMapper extends BaseMapper<ExtractionTask> {

    /**
     * 根据OA ID查询任务
     * 
     * @param oaid OA ID
     * @return 提数任务
     */
    @Select("SELECT id, oaid, title, applicant, applicant_email AS applicantEmail, " +
            "applicant_phone AS applicantPhone, extraction_status AS extractionStatus, " +
            "extraction_script AS extractionScript, data_source_id AS dataSourceId, " +
            "compliance_pdf_url AS compliancePdfUrl, sample_data_url AS sampleDataUrl, " +
            "result_file_url AS resultFileUrl, file_password AS filePassword, " +
            "total_records AS totalRecords, file_size AS fileSize, " +
            "execution_start_time AS executionStartTime, execution_end_time AS executionEndTime, " +
            "error_message AS errorMessage, create_by AS createBy, create_time AS createTime, " +
            "update_by AS updateBy, update_time AS updateTime, create_by_name AS createByName, " +
            "update_by_name AS updateByName, max_records_per_file AS maxRecordsPerFile " +
            "FROM extraction_task WHERE oaid = #{oaid} LIMIT 1")
    ExtractionTask findByOaid(@Param("oaid") String oaid);

    /**
     * 检查OA ID是否存在
     * 
     * @param oaid OA ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(1) FROM extraction_task WHERE oaid = #{oaid}")
    int countByOaid(@Param("oaid") String oaid);

    /**
     * 根据状态查询任务列表
     * 
     * @param status 任务状态
     * @return 任务列表
     */
    @Select("SELECT id, oaid, title, applicant, applicant_email AS applicantEmail, " +
            "applicant_phone AS applicantPhone, extraction_status AS extractionStatus, " +
            "extraction_script AS extractionScript, data_source_id AS dataSourceId, " +
            "compliance_pdf_url AS compliancePdfUrl, sample_data_url AS sampleDataUrl, " +
            "result_file_url AS resultFileUrl, file_password AS filePassword, " +
            "total_records AS totalRecords, file_size AS fileSize, " +
            "execution_start_time AS executionStartTime, execution_end_time AS executionEndTime, " +
            "error_message AS errorMessage, create_by AS createBy, create_time AS createTime, " +
            "update_by AS updateBy, update_time AS updateTime, create_by_name AS createByName, " +
            "update_by_name AS updateByName, max_records_per_file AS maxRecordsPerFile " +
            "FROM extraction_task WHERE extraction_status = #{status} ORDER BY create_time DESC")
    List<ExtractionTask> findByStatus(@Param("status") String status);

    /**
     * 查询可执行的任务列表
     * 
     * @return 可执行任务列表
     */
    @Select("SELECT id, oaid, title, applicant, applicant_email AS applicantEmail, " +
            "applicant_phone AS applicantPhone, extraction_status AS extractionStatus, " +
            "extraction_script AS extractionScript, data_source_id AS dataSourceId, " +
            "compliance_pdf_url AS compliancePdfUrl, sample_data_url AS sampleDataUrl, " +
            "result_file_url AS resultFileUrl, file_password AS filePassword, " +
            "total_records AS totalRecords, file_size AS fileSize, " +
            "execution_start_time AS executionStartTime, execution_end_time AS executionEndTime, " +
            "error_message AS errorMessage, create_by AS createBy, create_time AS createTime, " +
            "update_by AS updateBy, update_time AS updateTime, create_by_name AS createByName, " +
            "update_by_name AS updateByName, max_records_per_file AS maxRecordsPerFile " +
            "FROM extraction_task WHERE extraction_status = 'APPROVED' ORDER BY create_time ASC")
    List<ExtractionTask> findExecutableTasks();

    /**
     * 根据申请人查询任务列表
     * 
     * @param applicant 申请人
     * @return 任务列表
     */
    @Select("SELECT id, oaid, title, applicant, applicant_email AS applicantEmail, " +
            "applicant_phone AS applicantPhone, extraction_status AS extractionStatus, " +
            "extraction_script AS extractionScript, data_source_id AS dataSourceId, " +
            "compliance_pdf_url AS compliancePdfUrl, sample_data_url AS sampleDataUrl, " +
            "result_file_url AS resultFileUrl, file_password AS filePassword, " +
            "total_records AS totalRecords, file_size AS fileSize, " +
            "execution_start_time AS executionStartTime, execution_end_time AS executionEndTime, " +
            "error_message AS errorMessage, create_by AS createBy, create_time AS createTime, " +
            "update_by AS updateBy, update_time AS updateTime, create_by_name AS createByName, " +
            "update_by_name AS updateByName, max_records_per_file AS maxRecordsPerFile " +
            "FROM extraction_task WHERE applicant = #{applicant} ORDER BY create_time DESC")
    List<ExtractionTask> findByApplicant(@Param("applicant") String applicant);

    /**
     * 查询需要归档的任务（完成超过30天）
     * 
     * @param cutoffTime 截止时间
     * @return 需要归档的任务列表
     */
    @Select("SELECT id, oaid, title, applicant, applicant_email AS applicantEmail, " +
            "applicant_phone AS applicantPhone, extraction_status AS extractionStatus, " +
            "extraction_script AS extractionScript, data_source_id AS dataSourceId, " +
            "compliance_pdf_url AS compliancePdfUrl, sample_data_url AS sampleDataUrl, " +
            "result_file_url AS resultFileUrl, file_password AS filePassword, " +
            "total_records AS totalRecords, file_size AS fileSize, " +
            "execution_start_time AS executionStartTime, execution_end_time AS executionEndTime, " +
            "error_message AS errorMessage, create_by AS createBy, create_time AS createTime, " +
            "update_by AS updateBy, update_time AS updateTime, create_by_name AS createByName, " +
            "update_by_name AS updateByName, max_records_per_file AS maxRecordsPerFile " +
            "FROM extraction_task WHERE extraction_status = 'COMPLETED' AND execution_end_time < #{cutoffTime} AND is_archived = 0")
    List<ExtractionTask> findTasksToArchive(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * 更新任务状态
     * 
     * @param taskId 任务ID
     * @param status 新状态
     */
    @Update("UPDATE extraction_task SET extraction_status = #{status} WHERE id = #{taskId}")
    void updateStatus(@Param("taskId") Long taskId, @Param("status") String status);

    /**
     * 更新任务执行开始时间
     * 
     * @param taskId 任务ID
     * @param startTime 开始时间
     */
    @Update("UPDATE extraction_task SET execution_start_time = #{startTime}, extraction_status = 'EXECUTING' WHERE id = #{taskId}")
    void updateExecutionStartTime(@Param("taskId") Long taskId, @Param("startTime") LocalDateTime startTime);

    /**
     * 更新任务执行结果
     * 
     * @param taskId 任务ID
     * @param endTime 结束时间
     * @param status 最终状态
     * @param totalRecords 总记录数
     * @param fileSize 文件大小
     * @param resultFileUrl 结果文件URL
     * @param filePassword 文件密码
     */
    @Update("UPDATE extraction_task SET " +
            "execution_end_time = #{endTime}, " +
            "extraction_status = #{status}, " +
            "total_records = #{totalRecords}, " +
            "file_size = #{fileSize}, " +
            "result_file_url = #{resultFileUrl}, " +
            "file_password = #{filePassword} " +
            "WHERE id = #{taskId}")
    void updateExecutionResult(@Param("taskId") Long taskId,
                             @Param("endTime") LocalDateTime endTime,
                             @Param("status") String status,
                             @Param("totalRecords") Long totalRecords,
                             @Param("fileSize") Long fileSize,
                             @Param("resultFileUrl") String resultFileUrl,
                             @Param("filePassword") String filePassword);

    /**
     * 更新任务错误信息
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    @Update("UPDATE extraction_task SET extraction_status = 'FAILED', error_message = #{errorMessage} WHERE id = #{taskId}")
    void updateErrorMessage(@Param("taskId") Long taskId, @Param("errorMessage") String errorMessage);

    /**
     * 分页查询任务列表（复杂查询使用XML实现）
     * 
     * @param page 分页参数
     * @param request 查询条件
     * @return 任务列表VO
     */
    Page<TaskListVO> selectTaskPage(Page<TaskListVO> page, @Param("request") TaskQueryRequest request);
}
