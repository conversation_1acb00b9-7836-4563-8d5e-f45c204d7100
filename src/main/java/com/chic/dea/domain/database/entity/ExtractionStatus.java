package com.chic.dea.domain.database.entity;

/**
 * 提数任务状态枚举
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
public enum ExtractionStatus {
    
    /**
     * 草稿
     */
    DRAFT("草稿"),

    /**
     * 待合规审核
     */
    PENDING_COMPLIANCE_REVIEW("待合规审核"),

    /**
     * 合规审核通过
     */
    COMPLIANCE_APPROVED("合规审核通过"),

    /**
     * 合规审核不通过
     */
    COMPLIANCE_REJECTED("合规审核不通过"),

    /**
     * 待提数
     */
    PENDING_EXTRACTION("待提数"),

    /**
     * 数据生成中
     */
    DATA_GENERATING("数据生成中"),

    /**
     * 数据待发送
     */
    DATA_PENDING_SEND("数据待发送"),

    /**
     * 数据生成失败
     */
    DATA_GENERATION_FAILED("数据生成失败"),

    /**
     * 数据已发送
     */
    DATA_SENT("数据已发送"),

    /**
     * 数据发送失败
     */
    DATA_SEND_FAILED("数据发送失败"),

    /**
     * 待归档
     */
    PENDING_ARCHIVE("待归档"),

    /**
     * 已归档
     */
    ARCHIVED("已归档");

    private final String description;

    ExtractionStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    
    /**
     * 检查状态是否允许编辑
     *
     * @return true if editable
     */
    public boolean isEditable() {
        return this == DRAFT || this == COMPLIANCE_REJECTED || this == DATA_GENERATION_FAILED || this == DATA_SEND_FAILED;
    }

    /**
     * 检查状态是否可以执行提数操作
     *
     * @return true if executable
     */
    public boolean isExecutable() {
        return this == PENDING_EXTRACTION;
    }

    /**
     * 检查状态是否可以提交合规审核
     *
     * @return true if can submit for compliance review
     */
    public boolean canSubmitForComplianceReview() {
        return this == DRAFT;
    }

    /**
     * 检查状态是否可以进行合规审核
     *
     * @return true if can be reviewed for compliance
     */
    public boolean canBeReviewedForCompliance() {
        return this == PENDING_COMPLIANCE_REVIEW;
    }

    /**
     * 检查状态是否可以发送数据
     *
     * @return true if data can be sent
     */
    public boolean canSendData() {
        return this == DATA_PENDING_SEND;
    }

    /**
     * 检查状态是否可以归档
     *
     * @return true if can be archived
     */
    public boolean canBeArchived() {
        return this == DATA_SENT || this == PENDING_ARCHIVE;
    }
}
