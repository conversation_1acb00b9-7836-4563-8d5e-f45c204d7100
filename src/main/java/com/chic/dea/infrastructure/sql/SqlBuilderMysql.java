package com.chic.dea.infrastructure.sql;


import com.alibaba.druid.DbType;
import com.alibaba.druid.filter.Filter;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.druid.wall.WallFilter;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.DataSourceType;
import com.chic.dea.apis.model.vo.DataSourceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 * MySQL sql 构造器实现类
 * @classname SqlBuilderMysql.java
 * @date 2024/3/8 17:58
 * <AUTHOR>
 */
@Slf4j
@Service
public class SqlBuilderMysql extends AbstractSqlBuilder implements SqlBuilder {
    private static final String DATASOURCE_MYSQL_TEST_SQL = "SELECT 1 ";

    static {
        SqlBuilderContext.register(DataSourceType.MYSQL.getCode(), SqlBuilderMysql.class);
    }

    SqlBuilderMysql() {
        super();
        super.setDbType(DbType.mysql);
    }

    /**
     * 构建mysql数据源连接池
     * @param dataSourceVo
     * @return com.alibaba.druid.pool.DruidDataSource
     * <AUTHOR>
     * @date 2024/3/21 20:25
     */
    @Override
    public DruidDataSource buildDataSource(DataSourceVO dataSourceVo) {
        DruidDataSource druidDataSource = super.createDruidDataSource();
        druidDataSource.setUrl(dataSourceVo.getUrl());
        druidDataSource.setUsername(dataSourceVo.getUsername());
        druidDataSource.setPassword(dataSourceVo.getPassword());
        druidDataSource.setDriverClassName(JdbcConstants.MYSQL_DRIVER_6);
        druidDataSource.setValidationQuery(DATASOURCE_MYSQL_TEST_SQL);
        druidDataSource.setDbType(JdbcConstants.MYSQL);

        WallFilter wallFilter = new WallFilter();
        wallFilter.setDbType(JdbcConstants.MYSQL);
        wallFilter.setConfig(getWallConfig());
        ArrayList<Filter> filters = new ArrayList<>();
        filters.add(wallFilter);
        druidDataSource.setProxyFilters(filters);
        try {
            druidDataSource.init();
        } catch (SQLException e) {
            ApiException.newThrow(ErrorResultCode.PARAM_ERROR,"MYSQL数据源连接池初始化失败！");
        }
        return druidDataSource;
    }

    /**
     * 构建MySQL数据预览SQL，使用LIMIT实现限制
     * @param sql 原始SQL语句
     * @param limit 预览数据量
     * @return 包含LIMIT限制的预览SQL
     * <AUTHOR>
     * @date 2025/8/29
     */
    @Override
    public String buildPreviewSql(String sql, int limit) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new IllegalArgumentException("SQL语句不能为空");
        }
        if (limit <= 0) {
            throw new IllegalArgumentException("预览数据量必须大于0");
        }

        // 清理SQL语句，移除末尾的分号
        String cleanSql = cleanSql(sql);

        // 使用子查询和LIMIT实现限制
        return String.format("SELECT * FROM (%s) AS preview_table LIMIT %d", cleanSql, limit);
    }

}
