package com.chic.dea.infrastructure.sql;

import com.alibaba.druid.DbType;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.wall.WallConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import java.util.*;


/**
 * SqlBuilder 抽象类
 * @classname AbstractSqlBuilder.java
 * @date 2024/3/21 20:12
 * <AUTHOR>
 */
@Slf4j
@Data
public abstract class AbstractSqlBuilder {
    public DbType dbType = DbType.mysql;

    /**
     * 设置只读数据源
     *
     * @return com.alibaba.druid.wall.WallConfig
     * <AUTHOR>
     * @date 2024/3/21 20:15
     */
    public WallConfig getWallConfig() {
        WallConfig config = new WallConfig();
        /*是否允许执行 SELECT * FROM T 这样的语句。
        如果设置为 false，不允许执行 select * from t，
        但 select * from (select id, name from t) a。
        这个选项是防御程序通过调用 select * 获得数据表的结构信息。 */
        config.setSelectAllColumnAllow(false);
        //是否允许执行 SELECT 语句
        config.setSelectAllow(true);
        //SELECT 查询中是否允许 INTO 字句
        config.setSelectIntoAllow(false);
        //是否允许执行 DELETE 语句
        config.setDeleteAllow(false);
        //是否允许执行 UPDATE 语句
        config.setUpdateAllow(false);
        //是否允许执行 INSERT 语句
        config.setInsertAllow(false);
        //是否允许执行 REPLACE 语句
        config.setReplaceAllow(false);
        //是否允许执行 MERGE 语句，这个只在 Oracle 中有用
        config.setMergeAllow(false);
        //truncate 语句是危险，缺省打开，若需要自行关闭
        config.setTruncateAllow(false);
        // 	是否允许通过 jdbc 的 call 语法调用存储过程
        config.setCallAllow(false);
        //是否允许使用 SET 语法
        config.setSetAllow(true);
        // 	是否允许创建表
        config.setCreateTableAllow(false);
        //是否允许执行 Alter Table 语句
        config.setAlterTableAllow(false);
        // 	是否允许修改表
        config.setDropTableAllow(false);
        //是否允许语句中存在注释，Oracle 的用户不用担心，Wall 能够识别 hints 和注释的区别
        config.setCommentAllow(false);
        //是否允许非以上基本语句的其它语句，缺省关闭，通过这个选项就能够屏蔽 DDL。
        config.setNoneBaseStatementAllow(false);
        //是否允许一次执行多条语句，缺省关闭
        config.setMultiStatementAllow(false);
        //是否允许执行 mysql 的 use 语句，缺省打开
        config.setUseAllow(false);
        //是否允许执行 mysql 的 describe 语句，缺省打开
        config.setDescribeAllow(false);
        //是否允许执行 mysql 的 show 语句，缺省打开
        config.setShowAllow(false);
        //是否允许执行 commit 操作
        config.setCommitAllow(false);
        // 	是否允许执行 roll back 操作
        config.setRollbackAllow(false);
        //是否允许调用 Connection.getMetadata 方法，这个方法调用会暴露数据库的表信息
        config.setMetadataAllow(true);
        return config;
    }

    /**
     * 创建DruidDataSource数据源
     *
     * @return com.alibaba.druid.pool.DruidDataSource
     * <AUTHOR>
     * @date 2024/3/21 20:19
     */
    public DruidDataSource createDruidDataSource() {
        DruidDataSource ds = new DruidDataSource();
        ds.setInitialSize(1);
        ds.setMaxActive(10);
        ds.setLoginTimeout(30);
        ds.setMaxWait(300_000);
        ds.setTimeBetweenEvictionRunsMillis(60_000);
        ds.setMinEvictableIdleTimeMillis(300_000);
        ds.setConnectTimeout(300_000);
        ds.setSocketTimeout(300_000);
        /*失败后重连的次数*/
        ds.setConnectionErrorRetryAttempts(3);
        /*请求失败之后中断*/
        ds.setBreakAfterAcquireFailure(true);
        ds.setQueryTimeout(0);
        ds.setTestWhileIdle(true);
        ds.setTestOnBorrow(true);
        ds.setTestOnReturn(false);
        ds.setRemoveAbandoned(true);
        ds.setRemoveAbandonedTimeout(30);
        ds.setPoolPreparedStatements(true);
        ds.setMaxPoolPreparedStatementPerConnectionSize(20);
        return ds;
    }

    /**
     * 清理SQL语句，移除末尾的分号
     * @param sql 原始SQL语句
     * @return 清理后的SQL语句
     * <AUTHOR>
     * @date 2025/8/29
     */
    protected String cleanSql(String sql) {
        if (sql == null) {
            return null;
        }

        String cleanSql = sql.trim();
        while (cleanSql.endsWith(";")) {
            cleanSql = cleanSql.substring(0, cleanSql.length() - 1).trim();
        }
        return cleanSql;
    }

}
