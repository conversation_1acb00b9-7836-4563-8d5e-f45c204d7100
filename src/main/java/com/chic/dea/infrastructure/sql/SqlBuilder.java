package com.chic.dea.infrastructure.sql;

import com.alibaba.druid.pool.DruidDataSource;
import com.chic.dea.apis.model.vo.DataSourceVO;

/**
 * sql 构造器
 * <AUTHOR>
 * @classname SqlBuilder
 * @date 2024/3/21 18:09
 */
public interface SqlBuilder {

    /**
     * 构建buildDataSource
     * @param dataSourceVo
     * @return com.alibaba.druid.pool.DruidDataSource
     * <AUTHOR>
     * @date 2024/3/21 20:06
     */
    DruidDataSource buildDataSource(DataSourceVO dataSourceVo);

    /**
     * 构建数据预览SQL
     * @param sql 原始SQL语句
     * @param limit 预览数据量
     * @return 包含限制条件的预览SQL
     * <AUTHOR>
     * @date 2025/8/29
     */
    String buildPreviewSql(String sql, int limit);

    }
