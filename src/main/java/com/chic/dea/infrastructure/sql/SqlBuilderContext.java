package com.chic.dea.infrastructure.sql;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;


@Slf4j
@Component
public class SqlBuilderContext {

    private static final Map<String, Class<?>> providers = new ConcurrentHashMap<>();

    public static void register(String dataSourceType, Class<?> provider) {
        providers.put(dataSourceType, provider);
    }

    /**
     * 从容器中获取实现类
     * @param dataSourceType
     * @return com.chic.visual.infrastructure.sql.SqlBuilder
     * <AUTHOR>
     * @date 2024/3/21 20:09
     */
    public static SqlBuilder getSqlBuilderService(String dataSourceType) {
        log.info("获取SqlBuilder实现类,dataSourceType:{}", dataSourceType);
        Class<?> providerClazz = providers.get(dataSourceType);
        Assert.notNull(providerClazz, "dataSourceType 未注册 " + dataSourceType);
        Object bean = SpringUtil.getBean(providerClazz);
        Assert.notNull(bean, "spring容器中未找到实现类 " + providerClazz.getName());
        if (bean instanceof SqlBuilder) {
            return (SqlBuilder) bean;
        }
        throw new UnsupportedOperationException(providerClazz.getName() + " 必须实现 SqlBuilder");
    }


}
