package com.chic.dea.infrastructure.general.constants;

/**
 * @classname DataSourceType.java
 * @description
 * <p>
 *  数据源类型枚举类型
 * </p>
 * @date 2024/2/29 16:10
 * <AUTHOR>
 */
public enum DataSourceType {
    //1:mysql 2:pgsql 3:oracle 4:hive 5:clickhouse
    MYSQL("1","mysql"),
    PGSQL("2","pgsql"),
    ORACLE("3","oracle"),
    HIVE("4","hive"),
    CLICKHOUSE("5","clickhouse");

    private final String key;
    private final String value;

    DataSourceType(String key, String value){
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return this.key;
    }

    public String getValue() {
        return this.value;
    }
}
