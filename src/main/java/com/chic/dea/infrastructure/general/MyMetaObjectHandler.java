package com.chic.dea.infrastructure.general;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.chic.dea.apis.model.dto.UserDTO;
import com.chic.dea.infrastructure.general.util.LoginUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @classname MyMetaObjectHandler
 * @description TODO
 * @date 2025/1/13 16:30
 */
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject, "createBy", String.class, getCurrentUserCode());
        this.strictInsertFill(metaObject, "createByName", String.class, getCurrentUserName());
        this.strictInsertFill(metaObject, "updateBy", String.class, getCurrentUserCode());
        this.strictInsertFill(metaObject, "updateByName", String.class, getCurrentUserName());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.strictUpdateFill(metaObject, "updateBy", String.class, getCurrentUserCode());
        this.strictUpdateFill(metaObject, "updateByName", String.class, getCurrentUserName());
    }

    private String getCurrentUserCode() {
        UserDTO user = LoginUtil.getCurrentUser();
        return user.getUserId();
    }
    private String getCurrentUserName() {
        UserDTO user = LoginUtil.getCurrentUser();
        return user.getUserName();
    }
}
