package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 数据源更新请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class DataSourceUpdateRequest {

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * url
     */
    private String url;
    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;
    
    /**
     * 状态:1启用,0禁用
     */
    private Integer status;
}
