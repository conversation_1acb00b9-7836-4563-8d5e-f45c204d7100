package com.chic.dea.apis.model.dto;

import com.chic.dea.domain.service.SensitiveFieldService;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * SQL校验结果DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Builder
public class TaskSubmissionResponse {

    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 是否需要合规审批
     */
    private boolean requiresCompliance;
    /**
     * 涉及的敏感字段
     */
    List<SensitiveFieldService.SensitiveFieldCheckResult> sensitiveFieldCheckResults;

    /**
     * 创建成功结果
     */
    public static TaskSubmissionResponse success(Long taskId) {
        return TaskSubmissionResponse.builder()
                .taskId(taskId)
                .requiresCompliance(false)
                .build();
    }

    /**
     * 创建需要合规审批的结果
     */
    public static TaskSubmissionResponse requiresCompliance(Long taskId,List<SensitiveFieldService.SensitiveFieldCheckResult> sensitiveFieldCheckResults) {
        return TaskSubmissionResponse.builder()
                .taskId(taskId)
                .requiresCompliance(true)
                .sensitiveFieldCheckResults(sensitiveFieldCheckResults)
                .build();
    }
}
