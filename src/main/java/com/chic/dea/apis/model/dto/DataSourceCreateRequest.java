package com.chic.dea.apis.model.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 数据源创建请求DTO
 * 
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
public class DataSourceCreateRequest {

    /**
     * 数据源名称
     */
    @NotBlank(message = "数据源名称不能为空")
    private String name;

    /**
     * 数据源类型
     */
    @NotBlank(message = "数据源类型不能为空")
    private String type;

    /**
     * url
     */
    @NotBlank(message = "url")
    private String url;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 状态:1启用,0禁用
     */
    private Integer status = 1;
}
