package com.chic.dea.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.dea.apis.model.dto.OAInfoResponse;
import com.chic.dea.domain.service.OAService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * OA信息查询控制器
 * 
 * <AUTHOR>
 * @since 2024-12-11
 */
@Slf4j
@RestController
@RequestMapping("/api/oa")
@RequiredArgsConstructor
public class OAController {

    private final OAService oaService;

    /**
     * 查询OA信息
     */
    @GetMapping("/{oaId}")
    public Result<OAInfoResponse> getOAInfo(@PathVariable String oaId) {
        log.info("查询OA信息, oaId: {}", oaId);

        try {
            OAInfoResponse oaInfo = oaService.getOAInfoById(oaId);
            return Result.success(oaInfo);
        } catch (Exception e) {
            log.error("查询OA信息失败", e);
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询OA信息失败: " + e.getMessage()));
        }
    }


}
