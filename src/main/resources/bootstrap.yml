ci:
  environment:
    slug: ${CI_ENVIRONMENT_SLUG:hermes}
    cluster: ${CI_ENVIRONMENT_CLUSTER_NAME:hermes}

management:
  server:
    port: 18248
    base-path: /
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: "health,prometheus"
  endpoint:
    health:
      show-components: never
      show-details: always
      enabled: true
      probes:
        enabled: true
    prometheus:
      enabled: true
    metrics:
      enabled: true
  health:
    defaults:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      prometheus:
        enabled: true

