#!/bin/bash

# 数据源API测试脚本
# 测试获取所有数据源列表、根据ID获取数据源详情、更新数据源

BASE_URL="http://localhost:8248/data-extract-audit"
DATASOURCE_ID=3
TOKEN="eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl9leHBpcmVfdGltZV9rZXkiOjQ4MCwibG9naW5fYXBwX2lkX2tleSI6ImRhdGEtcXVhbGl0eSIsImxvZ2luX3VzZXJfbmFtZV9rZXkiOiLkvZXlhYnot4MiLCJsb2dpbl91c2VyX2lkX2tleSI6IjAwNDEyNzYxIiwibG9naW5fdXNlcl9rZXkiOiJjY2JlNmI5Ni03ZWVlLTQ2MjYtODhjYy00NjI0OTNhYmI4NDUifQ.6YnAqJqvsoh0f3kUFM8ZSVb_Gme_5HmGkeXQ_x7V_qGyrX-tcCB-uDALXeaNsb3M2j12R5pZ4sd4PAHGGIlOPA"

echo "=========================================="
echo "数据源API测试开始"
echo "=========================================="

# 1. 获取所有数据源列表
echo ""
echo "1. 测试获取所有数据源列表"
echo "GET ${BASE_URL}/api/datasources"
echo "----------------------------------------"
curl -X GET "${BASE_URL}/api/datasources" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="

# 2. 根据ID获取数据源详情
echo ""
echo "2. 测试根据ID获取数据源详情 (ID=${DATASOURCE_ID})"
echo "GET ${BASE_URL}/api/datasources/${DATASOURCE_ID}"
echo "----------------------------------------"
curl -X GET "${BASE_URL}/api/datasources/${DATASOURCE_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="

# 3. 更新数据源
echo ""
echo "3. 测试更新数据源 (ID=${DATASOURCE_ID})"
echo "PUT ${BASE_URL}/api/datasources/${DATASOURCE_ID}"
echo "----------------------------------------"

# 更新请求数据
UPDATE_DATA='{
  "name": "测试数据源_更新",
  "type": "MYSQL",
  "url": "****************************************",
  "username": "test_user_updated",
  "password": "test_password_updated",
  "status": 1
}'

echo "请求数据:"
echo "$UPDATE_DATA" | jq '.' 2>/dev/null || echo "$UPDATE_DATA"
echo ""

curl -X PUT "${BASE_URL}/api/datasources/${DATASOURCE_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -d "$UPDATE_DATA" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="

# 4. 再次获取数据源详情验证更新结果
echo ""
echo "4. 验证更新结果 - 再次获取数据源详情 (ID=${DATASOURCE_ID})"
echo "GET ${BASE_URL}/api/datasources/${DATASOURCE_ID}"
echo "----------------------------------------"
curl -X GET "${BASE_URL}/api/datasources/${DATASOURCE_ID}" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="

# 5. 测试获取启用的数据源列表
echo ""
echo "5. 测试获取启用的数据源列表"
echo "GET ${BASE_URL}/api/datasources?enabled=true"
echo "----------------------------------------"
curl -X GET "${BASE_URL}/api/datasources?enabled=true" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="

# 6. 测试连接数据源
echo ""
echo "6. 测试连接数据源 (ID=${DATASOURCE_ID})"
echo "POST ${BASE_URL}/api/datasources/${DATASOURCE_ID}/test"
echo "----------------------------------------"
curl -X POST "${BASE_URL}/api/datasources/${DATASOURCE_ID}/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -w "\nHTTP状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  | jq '.' 2>/dev/null || echo "响应内容无法解析为JSON"

echo ""
echo "=========================================="
echo "数据源API测试完成"
echo "=========================================="
